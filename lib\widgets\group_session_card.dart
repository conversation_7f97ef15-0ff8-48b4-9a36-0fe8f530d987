import 'package:flutter/material.dart';
import '../models/session_model.dart';
import '../utils/constants.dart';
import '../utils/date_utils.dart' as date_utils;

/// Card widget for displaying group session information
class GroupSessionCard extends StatelessWidget {
  final SessionModel session;
  final VoidCallback? onTap;
  final VoidCallback? onShare;
  final VoidCallback? onManage;

  const GroupSessionCard({
    super.key,
    required this.session,
    this.onTap,
    this.onShare,
    this.onManage,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 12),
              _buildSessionInfo(context),
              const SizedBox(height: 12),
              _buildParticipantInfo(context),
              if (onShare != null || onManage != null) ...[
                const SizedBox(height: 12),
                _buildActions(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatusText(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const Spacer(),
        Text(
          'Code: ${session.inviteCode}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.access_time,
              size: 16,
              color: Colors.grey,
            ),
            const SizedBox(width: 8),
            Text(
              'Created ${date_utils.DateUtils.formatRelativeTime(session.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        if (session.areaId.isNotEmpty) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Area: ${session.areaId}',
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildParticipantInfo(BuildContext context) {
    final participantCount = session.participants.length;
    final maxParticipants = session.maxParticipants;
    final isNearCapacity = participantCount >= (maxParticipants * 0.8);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNearCapacity ? Colors.orange[300]! : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.group,
            size: 20,
            color: isNearCapacity ? Colors.orange[600] : AppColors.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '$participantCount / $maxParticipants participants',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isNearCapacity ? Colors.orange[600] : null,
            ),
          ),
          const Spacer(),
          if (participantCount > 0)
            _buildParticipantAvatars(context),
        ],
      ),
    );
  }

  Widget _buildParticipantAvatars(BuildContext context) {
    final displayCount = session.participants.length > 3 ? 3 : session.participants.length;
    final hasMore = session.participants.length > 3;
    
    return Row(
      children: [
        ...List.generate(displayCount, (index) {
          return Container(
            margin: EdgeInsets.only(left: index > 0 ? 4 : 0),
            child: CircleAvatar(
              radius: 12,
              backgroundColor: AppColors.primary,
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }),
        if (hasMore)
          Container(
            margin: const EdgeInsets.only(left: 4),
            child: CircleAvatar(
              radius: 12,
              backgroundColor: Colors.grey[400],
              child: Text(
                '+${session.participants.length - 3}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 9,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      children: [
        if (onShare != null)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onShare,
              icon: const Icon(Icons.share, size: 16),
              label: const Text('Share'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: BorderSide(color: AppColors.primary),
              ),
            ),
          ),
        if (onShare != null && onManage != null)
          const SizedBox(width: 12),
        if (onManage != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onManage,
              icon: const Icon(Icons.settings, size: 16),
              label: const Text('Manage'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (session.status) {
      case SessionStatus.active:
        return Colors.green;
      case SessionStatus.completed:
        return Colors.blue;
      case SessionStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText() {
    switch (session.status) {
      case SessionStatus.active:
        return 'Active';
      case SessionStatus.completed:
        return 'Completed';
      case SessionStatus.cancelled:
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }
}