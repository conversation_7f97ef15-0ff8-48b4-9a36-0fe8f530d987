import 'package:flutter/material.dart';

/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'SafeStride';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  
  // Session Configuration
  static const int maxParticipantsFree = 3;
  static const int maxParticipantsPremium = 10;
  static const int inviteCodeLength = 6;
  static const int sessionTimeoutMinutes = 120;
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
}

/// Application color constants
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFFBBDEFB);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF4CAF50);
  static const Color secondaryDark = Color(0xFF388E3C);
  static const Color secondaryLight = Color(0xFFC8E6C9);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Neutral Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF212121);
  static const Color onBackground = Color(0xFF212121);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
}

/// Database constants
class DatabaseConstants {
  // Table Names
  static const String usersTable = 'users';
  static const String sessionsTable = 'sessions';
  static const String areasTable = 'areas';
  static const String sitesTable = 'sites';
  static const String findingsTable = 'findings';
  static const String joinRequestsTable = 'join_requests';
  
  // Database Version
  static const int databaseVersion = 1;
  static const String databaseName = 'safestride.db';
}

/// Firebase collection names
class FirebaseConstants {
  static const String usersCollection = 'users';
  static const String sessionsCollection = 'sessions';
  static const String areasCollection = 'areas';
  static const String sitesCollection = 'sites';
  static const String findingsCollection = 'findings';
  static const String joinRequestsCollection = 'join_requests';
}

/// Validation constants
class ValidationConstants {
  static const int minPasswordLength = 8;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;
  static const int maxDescriptionLength = 500;
  
  // Regular expressions
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phoneRegex = r'^[\+]?[1-9][\d]{0,15}$';
}