/// Enum for join request status
enum JoinRequestStatus {
  pending,
  accepted,
  rejected,
}

/// Join request data model for group walkabouts
class JoinRequest {
  final String id;
  final String sessionId;
  final String observerId;
  final JoinRequestStatus status;
  final DateTime createdAt;
  final bool synced;

  const JoinRe<PERSON>({
    required this.id,
    required this.sessionId,
    required this.observerId,
    required this.status,
    required this.createdAt,
    this.synced = false,
  });

  /// Create JoinRequest from map
  factory JoinRequest.fromMap(Map<String, dynamic> map) {
    return JoinRequest(
      id: map['id'] as String,
      sessionId: map['session_id'] as String,
      observerId: map['observer_id'] as String,
      status: JoinRequestStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => JoinRequestStatus.pending,
      ),
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
      synced: map['synced'] == 1 || map['synced'] == true,
    );
  }

  /// Convert JoinRequest to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'session_id': sessionId,
      'observer_id': observerId,
      'status': status.name,
      'created_at': createdAt.millisecondsSinceEpoch,
      'synced': synced ? 1 : 0,
    };
  }

  /// Create copy with updated fields
  JoinRequest copyWith({
    String? id,
    String? sessionId,
    String? observerId,
    JoinRequestStatus? status,
    DateTime? createdAt,
    bool? synced,
  }) {
    return JoinRequest(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      observerId: observerId ?? this.observerId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      synced: synced ?? this.synced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JoinRequest &&
        other.id == id &&
        other.sessionId == sessionId &&
        other.observerId == observerId &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.synced == synced;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        sessionId.hashCode ^
        observerId.hashCode ^
        status.hashCode ^
        createdAt.hashCode ^
        synced.hashCode;
  }

  @override
  String toString() {
    return 'JoinRequest(id: $id, sessionId: $sessionId, observerId: $observerId, status: $status, createdAt: $createdAt, synced: $synced)';
  }
}