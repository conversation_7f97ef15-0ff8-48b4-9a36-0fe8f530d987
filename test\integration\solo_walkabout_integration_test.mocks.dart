// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/integration/solo_walkabout_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;
import 'dart:ui' as _i12;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/models/user_model.dart' as _i11;
import 'package:safestride/providers/auth_provider.dart' as _i9;
import 'package:safestride/repositories/area_repository.dart' as _i6;
import 'package:safestride/repositories/checklist_repository.dart' as _i7;
import 'package:safestride/repositories/finding_repository.dart' as _i4;
import 'package:safestride/repositories/repository_factory.dart' as _i8;
import 'package:safestride/repositories/session_repository.dart' as _i3;
import 'package:safestride/repositories/site_repository.dart' as _i5;
import 'package:safestride/repositories/user_repository.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserRepository_0 extends _i1.SmartFake
    implements _i2.UserRepository {
  _FakeUserRepository_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSessionRepository_1 extends _i1.SmartFake
    implements _i3.SessionRepository {
  _FakeSessionRepository_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFindingRepository_2 extends _i1.SmartFake
    implements _i4.FindingRepository {
  _FakeFindingRepository_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSiteRepository_3 extends _i1.SmartFake
    implements _i5.SiteRepository {
  _FakeSiteRepository_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAreaRepository_4 extends _i1.SmartFake
    implements _i6.AreaRepository {
  _FakeAreaRepository_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeChecklistRepository_5 extends _i1.SmartFake
    implements _i7.ChecklistRepository {
  _FakeChecklistRepository_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHybridUserRepository_6 extends _i1.SmartFake
    implements _i8.HybridUserRepository {
  _FakeHybridUserRepository_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i9.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  bool get isOffline => (super.noSuchMethod(
        Invocation.getter(#isOffline),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i10.Future<bool> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<bool> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<bool> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? name,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUpWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
            #name: name,
          },
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<bool> resetPassword(String? email) => (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [email],
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<bool> tryOfflineAuthentication({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #tryOfflineAuthentication,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<bool> updateUserRole(_i11.UserRole? role) => (super.noSuchMethod(
        Invocation.method(
          #updateUserRole,
          [role],
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  _i10.Future<bool> updateUserSubscription(
          _i11.SubscriptionType? subscription) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserSubscription,
          [subscription],
        ),
        returnValue: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  void clearError() => super.noSuchMethod(
        Invocation.method(
          #clearError,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setCurrentUser(_i11.UserModel? user) => super.noSuchMethod(
        Invocation.method(
          #setCurrentUser,
          [user],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i12.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i12.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RepositoryFactory].
///
/// See the documentation for Mockito's code generation for more information.
class MockRepositoryFactory extends _i1.Mock implements _i8.RepositoryFactory {
  MockRepositoryFactory() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i10.Future<_i2.UserRepository> getUserRepository() => (super.noSuchMethod(
        Invocation.method(
          #getUserRepository,
          [],
        ),
        returnValue:
            _i10.Future<_i2.UserRepository>.value(_FakeUserRepository_0(
          this,
          Invocation.method(
            #getUserRepository,
            [],
          ),
        )),
      ) as _i10.Future<_i2.UserRepository>);

  @override
  _i3.SessionRepository getSessionRepository() => (super.noSuchMethod(
        Invocation.method(
          #getSessionRepository,
          [],
        ),
        returnValue: _FakeSessionRepository_1(
          this,
          Invocation.method(
            #getSessionRepository,
            [],
          ),
        ),
      ) as _i3.SessionRepository);

  @override
  _i4.FindingRepository getFindingRepository() => (super.noSuchMethod(
        Invocation.method(
          #getFindingRepository,
          [],
        ),
        returnValue: _FakeFindingRepository_2(
          this,
          Invocation.method(
            #getFindingRepository,
            [],
          ),
        ),
      ) as _i4.FindingRepository);

  @override
  _i5.SiteRepository getSiteRepository() => (super.noSuchMethod(
        Invocation.method(
          #getSiteRepository,
          [],
        ),
        returnValue: _FakeSiteRepository_3(
          this,
          Invocation.method(
            #getSiteRepository,
            [],
          ),
        ),
      ) as _i5.SiteRepository);

  @override
  _i6.AreaRepository getAreaRepository() => (super.noSuchMethod(
        Invocation.method(
          #getAreaRepository,
          [],
        ),
        returnValue: _FakeAreaRepository_4(
          this,
          Invocation.method(
            #getAreaRepository,
            [],
          ),
        ),
      ) as _i6.AreaRepository);

  @override
  _i7.ChecklistRepository getChecklistRepository() => (super.noSuchMethod(
        Invocation.method(
          #getChecklistRepository,
          [],
        ),
        returnValue: _FakeChecklistRepository_5(
          this,
          Invocation.method(
            #getChecklistRepository,
            [],
          ),
        ),
      ) as _i7.ChecklistRepository);

  @override
  _i2.UserRepository getLocalUserRepository() => (super.noSuchMethod(
        Invocation.method(
          #getLocalUserRepository,
          [],
        ),
        returnValue: _FakeUserRepository_0(
          this,
          Invocation.method(
            #getLocalUserRepository,
            [],
          ),
        ),
      ) as _i2.UserRepository);

  @override
  _i2.UserRepository getRemoteUserRepository() => (super.noSuchMethod(
        Invocation.method(
          #getRemoteUserRepository,
          [],
        ),
        returnValue: _FakeUserRepository_0(
          this,
          Invocation.method(
            #getRemoteUserRepository,
            [],
          ),
        ),
      ) as _i2.UserRepository);

  @override
  _i8.HybridUserRepository createHybridUserRepository() => (super.noSuchMethod(
        Invocation.method(
          #createHybridUserRepository,
          [],
        ),
        returnValue: _FakeHybridUserRepository_6(
          this,
          Invocation.method(
            #createHybridUserRepository,
            [],
          ),
        ),
      ) as _i8.HybridUserRepository);
}
