import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../repositories/user_repository.dart';

/// Authentication state provider using Provider pattern
class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  final UserRepository _userRepository;

  UserModel? _currentUser;
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;
  bool _isDemoUser = false;

  AuthProvider(this._authService, this._userRepository) {
    _initialize();
  }

  /// Current authenticated user
  UserModel? get currentUser => _currentUser;

  /// Whether the provider is initialized
  bool get isInitialized => _isInitialized;

  /// Loading state
  bool get isLoading => _isLoading;

  /// Error message
  String? get error => _error;

  /// Whether user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Whether device is offline
  bool get isOffline => _authService.isOffline;

  /// Initialize authentication state
  Future<void> _initialize() async {
    try {
      _setLoading(true);

      // Listen to Firebase auth state changes
      FirebaseAuth.instance.authStateChanges().listen(_onAuthStateChanged);

      // Check if there's a current Firebase user
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser.uid);
      }

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Handle Firebase auth state changes
  Future<void> _onAuthStateChanged(User? firebaseUser) async {
    try {
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser.uid);
      } else {
        // Don't clear current user if it's a demo user
        if (!_isDemoUser) {
          _currentUser = null;
          notifyListeners();
        }
      }
    } catch (e) {
      _setError('Authentication state change error: $e');
    }
  }

  /// Load user data from repository
  Future<void> _loadUserData(String userId) async {
    try {
      final user = await _userRepository.getUserById(userId);
      _currentUser = user;
      _isDemoUser = false; // Reset demo user flag when loading real user
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load user data: $e');
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in with Google
  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.signInWithGoogle();

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up with email and password
  Future<bool> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.signOut();
      _currentUser = null;
      _isDemoUser = false; // Reset demo user flag on sign out
      notifyListeners();
    } catch (e) {
      _setError('Sign out failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Try offline authentication
  Future<bool> tryOfflineAuthentication({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.tryOfflineAuthentication(
        email: email,
        password: password,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update user role
  Future<bool> updateUserRole(UserRole role) async {
    if (_currentUser == null) return false;

    try {
      _setLoading(true);
      _clearError();

      final updatedUser = _currentUser!.copyWith(role: role);
      await _userRepository.updateUser(updatedUser);

      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update role: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update user subscription
  Future<bool> updateUserSubscription(SubscriptionType subscription) async {
    if (_currentUser == null) return false;

    try {
      _setLoading(true);
      _clearError();

      final updatedUser = _currentUser!.copyWith(subscription: subscription);
      await _userRepository.updateUser(updatedUser);

      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update subscription: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Clear error message
  void clearError() {
    _clearError();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Set current user (for testing purposes)
  void setCurrentUser(UserModel user) {
    _currentUser = user;
    _isDemoUser = true;
    _clearError();
    notifyListeners();
  }
}
