# Tech Stack

### Cloud Infrastructure
- **Provider:** Google Cloud Platform (via Firebase)
- **Key Services:** Firebase Auth, Firestore, Cloud Functions, Cloud Storage, Analytics
- **Deployment Regions:** Multi-region (automatic via Firebase)

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|----------|
| **Mobile Framework** | Flutter | 3.16.0 | Cross-platform mobile development | Single codebase, native performance, strong ecosystem |
| **Language** | Dart | 3.2.0 | Primary development language | Optimized for Flutter, strong typing, null safety |
| **Backend** | Firebase | Latest | Serverless backend services | Integrated ecosystem, real-time capabilities, offline support |
| **Authentication** | Firebase Auth | Latest | User authentication & SSO | Built-in providers, secure, integrates with Firestore rules |
| **Database** | Cloud Firestore | Latest | Primary cloud database | Real-time sync, offline support, scalable NoSQL |
| **Local Storage** | SQLite (sqflite) | 2.3.0 | Offline data caching | Reliable local storage, SQL queries, Flutter integration |
| **AI/ML** | TensorFlow Lite | 0.10.0 | On-device AI inference | Offline capability, optimized for mobile, privacy-preserving |
| **Image Processing** | image_picker | 1.0.4 | Photo capture & compression | Native camera integration, image optimization |
| **QR Codes** | qr_flutter, mobile_scanner | 4.1.0, 5.2.3 | QR generation & scanning | Team collaboration features |
| **PDF Generation** | pdf | 3.10.4 | Report generation | Compliance reporting requirements |
| **State Management** | Provider | 6.1.1 | Application state management | Simple, performant, Flutter-recommended |
| **Notifications** | flutter_local_notifications | 16.3.0 | Local push notifications | Task reminders, offline notifications |

