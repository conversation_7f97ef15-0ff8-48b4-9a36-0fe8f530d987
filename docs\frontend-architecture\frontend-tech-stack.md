# Frontend Tech Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|----------|
| **Framework** | Flutter | 3.16.0 | Cross-platform mobile development |
| **Language** | Dart | 3.2.0 | Primary development language |
| **State Management** | Provider | 6.1.1 | Application state management |
| **Local Database** | sqflite | 2.3.0 | Offline data storage |
| **Navigation** | go_router | 12.1.3 | Declarative routing |
| **Camera** | image_picker | 1.0.4 | Photo capture and selection |
| **QR Codes** | qr_flutter, mobile_scanner | 4.1.0, 5.2.3 | QR generation and scanning |
| **PDF Generation** | pdf | 3.10.4 | Report generation |
| **Notifications** | flutter_local_notifications | 16.3.0 | Local notifications |
| **Connectivity** | connectivity_plus | 5.0.2 | Network status monitoring |
| **Secure Storage** | flutter_secure_storage | 9.0.0 | Credential storage |
| **AI/ML** | tflite_flutter | 0.10.0 | On-device AI inference |
