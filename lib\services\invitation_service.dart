import 'dart:convert';
import 'dart:typed_data';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

/// Service for handling invitation generation and sharing
class InvitationService {
  static const String _baseUrl = 'safestride://join';

  /// Generate invitation link for a session
  Future<String> generateInviteLink({
    required String sessionId,
    required String inviteCode,
  }) async {
    return '$_baseUrl/$sessionId/$inviteCode';
  }

  /// Generate QR code as base64 string
  Future<String> generateQRCode({
    required String inviteLink,
  }) async {
    try {
      final qrValidationResult = QrValidator.validate(
        data: inviteLink,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      );

      if (qrValidationResult.status == QrValidationStatus.valid) {
        final qrCode = qrValidationResult.qrCode!;
        final painter = QrPainter.withQr(
          qr: qrCode,
          color: Colors.black,
          emptyColor: Colors.white,
          gapless: false,
        );

        // Convert to image bytes
        final picData = await painter.toImageData(200);
        if (picData != null) {
          final bytes = picData.buffer.asUint8List();
          return base64Encode(bytes);
        }
      }
      throw Exception('Failed to generate QR code');
    } catch (e) {
      throw Exception('QR code generation failed: $e');
    }
  }

  /// Share invitation via multiple channels
  Future<void> shareInvitation({
    required String inviteLink,
    required String sessionId,
    String? customMessage,
  }) async {
    final message = customMessage ?? 
        'Join my SafeStride group walkabout! Use this link: $inviteLink';
    
    await Share.share(
      message,
      subject: 'SafeStride Group Walkabout Invitation',
    );
  }

  /// Share invitation via WhatsApp
  Future<void> shareViaWhatsApp({
    required String inviteLink,
    required String sessionId,
    String? customMessage,
  }) async {
    final message = customMessage ?? 
        'Join my SafeStride group walkabout! Use this link: $inviteLink';
    
    final whatsappUrl = 'whatsapp://send?text=${Uri.encodeComponent(message)}';
    
    await Share.share(
      message,
      subject: 'SafeStride Group Walkabout Invitation',
    );
  }

  /// Share invitation via SMS
  Future<void> shareViaSMS({
    required String inviteLink,
    required String sessionId,
    String? phoneNumber,
    String? customMessage,
  }) async {
    final message = customMessage ?? 
        'Join my SafeStride group walkabout! Use this link: $inviteLink';
    
    await Share.share(
      message,
      subject: 'SafeStride Group Walkabout Invitation',
    );
  }

  /// Share invitation via Email
  Future<void> shareViaEmail({
    required String inviteLink,
    required String sessionId,
    String? email,
    String? customMessage,
  }) async {
    final message = customMessage ?? 
        'Join my SafeStride group walkabout! Use this link: $inviteLink';
    
    await Share.share(
      message,
      subject: 'SafeStride Group Walkabout Invitation',
    );
  }

  /// Parse invitation link to extract session ID and invite code
  Map<String, String>? parseInviteLink(String inviteLink) {
    try {
      final uri = Uri.parse(inviteLink);
      if (uri.scheme == 'safestride' && uri.host == 'join') {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 2) {
          return {
            'sessionId': pathSegments[0],
            'inviteCode': pathSegments[1],
          };
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Validate invitation link format
  bool isValidInviteLink(String inviteLink) {
    final parsed = parseInviteLink(inviteLink);
    return parsed != null && 
           parsed['sessionId']?.isNotEmpty == true && 
           parsed['inviteCode']?.isNotEmpty == true;
  }

  /// Generate QR code widget for display
  Widget generateQRWidget({
    required String inviteLink,
    double size = 200.0,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white,
  }) {
    return QrImageView(
      data: inviteLink,
      version: QrVersions.auto,
      size: size,
      foregroundColor: foregroundColor,
      backgroundColor: backgroundColor,
      errorCorrectionLevel: QrErrorCorrectLevel.M,
    );
  }
}