import 'dart:convert';
import 'package:uuid/uuid.dart';
import '../models/session_model.dart';
import '../models/user_model.dart';
import '../repositories/session_repository.dart';
import 'invitation_service.dart';

/// Service for managing group walkabout sessions
class GroupSessionService {
  final SessionRepository _sessionRepository;
  final InvitationService _invitationService;
  final Uuid _uuid = const Uuid();

  GroupSessionService({
    required SessionRepository sessionRepository,
    required InvitationService invitationService,
  })  : _sessionRepository = sessionRepository,
        _invitationService = invitationService;

  /// Create a new group session
  Future<SessionModel> createGroupSession({
    required String leaderId,
    required String areaId,
    required int maxParticipants,
  }) async {
    final sessionId = _uuid.v4();
    final inviteCode = _generateInviteCode();

    // Generate invitation link and QR code
    final inviteLink = await _invitationService.generateInviteLink(
      sessionId: sessionId,
      inviteCode: inviteCode,
    );
    
    final qrCode = await _invitationService.generateQRCode(
      inviteLink: inviteLink,
    );

    final session = SessionModel(
      id: sessionId,
      type: SessionType.group,
      leaderId: leaderId,
      areaId: areaId,
      status: SessionStatus.active,
      inviteCode: inviteCode,
      inviteLink: inviteLink,
      qrCode: qrCode,
      participants: [],
      maxParticipants: maxParticipants,
      createdAt: DateTime.now(),
    );

    await _sessionRepository.createSession(session);
    return session;
  }

  /// Add participant to group session
  Future<SessionModel> addParticipant({
    required String sessionId,
    required String participantId,
  }) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    if (session.participants.contains(participantId)) {
      throw Exception('Participant already in session');
    }

    if (session.participants.length >= session.maxParticipants) {
      throw Exception('Session is full');
    }

    final updatedParticipants = [...session.participants, participantId];
    final updatedSession = session.copyWith(participants: updatedParticipants);
    
    await _sessionRepository.updateSession(updatedSession);
    return updatedSession;
  }

  /// Remove participant from group session
  Future<SessionModel> removeParticipant({
    required String sessionId,
    required String participantId,
  }) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final updatedParticipants = session.participants
        .where((id) => id != participantId)
        .toList();
    
    final updatedSession = session.copyWith(participants: updatedParticipants);
    
    await _sessionRepository.updateSession(updatedSession);
    return updatedSession;
  }

  /// Get group session by ID
  Future<SessionModel?> getGroupSession(String sessionId) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    if (session?.type == SessionType.group) {
      return session;
    }
    return null;
  }

  /// Get all group sessions for a leader
  Future<List<SessionModel>> getLeaderGroupSessions(String leaderId) async {
    final sessions = await _sessionRepository.getSessionsByLeader(leaderId);
    return sessions.where((s) => s.type == SessionType.group).toList();
  }

  /// Get active sessions for a user (as leader or participant)
  Future<List<SessionModel>> getActiveSessionsForUser(String userId) async {
    final leaderSessions = await getLeaderGroupSessions(userId);
    final activeSessions = leaderSessions.where((s) => s.status == SessionStatus.active).toList();
    
    // TODO: Also get sessions where user is a participant
    // This would require a new repository method to query by participant
    
    return activeSessions;
  }

  /// Join session by invite code
  Future<SessionModel> joinSessionByInviteCode({
    required String inviteCode,
    required String observerId,
  }) async {
    // Find session by invite code
    final session = await _sessionRepository.getSessionByInviteCode(inviteCode);
    if (session == null) {
      throw Exception('Invalid invite code');
    }

    if (session.status != SessionStatus.active) {
      throw Exception('Session is not active');
    }

    // Add observer as participant
    return await addParticipant(
      sessionId: session.id,
      participantId: observerId,
    );
  }

  /// Validate invite code
  Future<bool> validateInviteCode({
    required String sessionId,
    required String inviteCode,
  }) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    return session?.inviteCode == inviteCode && 
           session?.status == SessionStatus.active;
  }

  /// Generate a unique 6-character invite code
  String _generateInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var result = '';
    
    for (int i = 0; i < 6; i++) {
      result += chars[(random + i) % chars.length];
    }
    
    return result;
  }

  /// Complete group session
  Future<SessionModel> completeSession(String sessionId) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final updatedSession = session.copyWith(
      status: SessionStatus.completed,
      completedAt: DateTime.now(),
    );
    
    await _sessionRepository.updateSession(updatedSession);
    return updatedSession;
  }

  /// Cancel group session
  Future<SessionModel> cancelSession(String sessionId) async {
    final session = await _sessionRepository.getSessionById(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final updatedSession = session.copyWith(
      status: SessionStatus.cancelled,
    );
    
    await _sessionRepository.updateSession(updatedSession);
    return updatedSession;
  }
}