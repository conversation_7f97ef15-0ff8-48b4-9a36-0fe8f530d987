import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../utils/app_theme.dart';
import '../widgets/auth_dialogs.dart';
import '../widgets/health_check_widget.dart';

/// Welcome screen - the main entry point of the app
class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  bool _showHealthCheck = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo and title section
              Column(
                children: [
                  // App logo placeholder
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius:
                          BorderRadius.circular(AppTheme.radiusXLarge),
                    ),
                    child: Icon(
                      Icons.security,
                      size: 60,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),

                  // Welcome message
                  Text(
                    'Welcome to SafeStride',
                    style: AppTheme.headingLarge.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),

                  Text(
                    'Industrial Safety Inspection Platform',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingXLarge * 2),

              // Action buttons
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ElevatedButton(
                    onPressed: () => _showLoginDialog(context),
                    child: const Text('Sign In'),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  OutlinedButton(
                    onPressed: () => _showRegisterDialog(context),
                    child: const Text('Create Account'),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showHealthCheck = !_showHealthCheck;
                      });
                    },
                    child: Text(_showHealthCheck
                        ? 'Hide Health Check'
                        : 'Show Health Check'),
                  ),
                ],
              ),

              // Health check section
              if (_showHealthCheck) ...[
                const SizedBox(height: AppTheme.spacingLarge),
                const HealthCheckWidget(),
              ],

              const Spacer(),

              // App version and status
              Column(
                children: [
                  Consumer<AuthService?>(
                    builder: (context, authService, child) {
                      return Text(
                        authService?.isAuthenticated == true
                            ? 'Signed in as ${authService?.currentUser?.displayName ?? "User"}'
                            : 'Not signed in',
                        style: AppTheme.bodySmall.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6),
                        ),
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  Text(
                    'Version 1.0.0',
                    style: AppTheme.bodySmall.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLoginDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SignInDialog(),
    );
  }

  void _showRegisterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SignUpDialog(),
    );
  }
}
