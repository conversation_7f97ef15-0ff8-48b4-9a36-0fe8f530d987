import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';
import '../utils/app_theme.dart';
import '../widgets/health_check_widget.dart';
import 'home_screen.dart';

/// Welcome screen - the main entry point of the app
class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  bool _showHealthCheck = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo and title section
              Column(
                children: [
                  // App logo placeholder
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius:
                          BorderRadius.circular(AppTheme.radiusXLarge),
                    ),
                    child: Icon(
                      Icons.security,
                      size: 60,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),

                  // Welcome message
                  Text(
                    'Welcome to SafeStride',
                    style: AppTheme.headingLarge.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),

                  Text(
                    'Industrial Safety Inspection Platform',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingXLarge * 2),

              // Action buttons
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ElevatedButton(
                    onPressed: () => _showLoginDialog(context),
                    child: const Text('Sign In'),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  OutlinedButton(
                    onPressed: () => _showRegisterDialog(context),
                    child: const Text('Create Account'),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showHealthCheck = !_showHealthCheck;
                      });
                    },
                    child: Text(_showHealthCheck
                        ? 'Hide Health Check'
                        : 'Show Health Check'),
                  ),
                ],
              ),

              // Health check section
              if (_showHealthCheck) ...[
                const SizedBox(height: AppTheme.spacingLarge),
                const HealthCheckWidget(),
              ],

              const Spacer(),

              // App version and status
              Column(
                children: [
                  Consumer<AuthService?>(
                    builder: (context, authService, child) {
                      return Text(
                        authService?.isAuthenticated == true
                            ? 'Signed in as ${authService?.currentUser?.displayName ?? "User"}'
                            : 'Not signed in',
                        style: AppTheme.bodySmall.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6),
                        ),
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  Text(
                    'Version 1.0.0',
                    style: AppTheme.bodySmall.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLoginDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign In'),
        content: const Text('For testing purposes, you can sign in as a demo user. This will bypass Firebase authentication.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _signInAsTestUser(context),
            child: const Text('Demo Sign In'),
          ),
        ],
      ),
    );
  }

  Future<void> _signInAsTestUser(BuildContext context) async {
    Navigator.of(context).pop(); // Close dialog

    // Show loading indicator
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Signing in...'),
            ],
          ),
        ),
      );
    }

    try {
      // Add timeout to prevent infinite loading
      await Future.any([
        _performDemoSignIn(context),
        Future.delayed(const Duration(seconds: 10), () => throw TimeoutException('Sign in timed out', const Duration(seconds: 10))),
      ]);
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to sign in: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _performDemoSignIn(BuildContext context) async {
    try {
      // Add a small delay to simulate authentication
      await Future.delayed(const Duration(milliseconds: 500));

      // Create a test user
      final testUser = UserModel(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      // Get the auth provider before any navigation
      AuthProvider? authProvider;
      if (context.mounted) {
        try {
          authProvider = Provider.of<AuthProvider>(context, listen: false);
        } catch (e) {
          print('AuthProvider not available: $e');
          throw Exception('Authentication provider not available');
        }
      }

      // Set the current user in the auth provider
      if (authProvider != null) {
        authProvider.setCurrentUser(testUser);
      } else {
        throw Exception('Failed to access authentication provider');
      }

      // Close loading dialog first
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Small delay to ensure dialog is closed
      await Future.delayed(const Duration(milliseconds: 100));

      // Navigate to home screen
      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const HomeScreen(),
          ),
        );
      }
    } catch (e) {
      print('Demo sign in error: $e');
      rethrow;
    }
  }

  void _showRegisterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Account'),
        content: const Text(
            'Registration functionality will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
