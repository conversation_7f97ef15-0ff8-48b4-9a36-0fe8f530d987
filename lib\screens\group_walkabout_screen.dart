import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/session_model.dart';
import '../models/user_model.dart';
import '../providers/auth_provider.dart';
import '../providers/group_session_provider.dart';
import '../widgets/group_session_card.dart';
import '../widgets/create_session_dialog.dart';
import '../widgets/join_session_dialog.dart';
import '../utils/constants.dart';

/// Screen for managing group walkabout sessions
class GroupWalkaboutScreen extends StatefulWidget {
  const GroupWalkaboutScreen({super.key});

  @override
  State<GroupWalkaboutScreen> createState() => _GroupWalkaboutScreenState();
}

class _GroupWalkaboutScreenState extends State<GroupWalkaboutScreen> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadActiveSessions();
    });
  }

  Future<void> _loadActiveSessions() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final groupSessionProvider = Provider.of<GroupSessionProvider>(context, listen: false);
    final user = authProvider.currentUser;
    
    if (user == null) return;

    await groupSessionProvider.loadActiveSessions(user.id);
  }

  Future<void> _createNewSession() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final groupSessionProvider = Provider.of<GroupSessionProvider>(context, listen: false);
    final user = authProvider.currentUser;
    
    if (user == null) return;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CreateSessionDialog(
        maxParticipants: user.subscription == SubscriptionType.premium ? 10 : 3,
      ),
    );

    if (result != null) {
      final session = await groupSessionProvider.createSession(
        leaderId: user.id,
        areaId: result['areaId'] as String,
        maxParticipants: result['maxParticipants'] as int,
      );

      if (session != null && mounted) {
        Navigator.pushNamed(
          context,
          '/session-management',
          arguments: session.id,
        );
      } else if (mounted && groupSessionProvider.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(groupSessionProvider.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _joinSession() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final groupSessionProvider = Provider.of<GroupSessionProvider>(context, listen: false);
    final user = authProvider.currentUser;
    
    if (user == null) return;

    final result = await showDialog<String>(
      context: context,
      builder: (context) => const JoinSessionDialog(),
    );

    if (result != null && result.isNotEmpty) {
      final session = await groupSessionProvider.joinSessionByInviteCode(
        inviteCode: result,
        observerId: user.id,
      );

      if (session != null && mounted) {
        Navigator.pushNamed(
          context,
          '/session-observer',
          arguments: session.id,
        );
      } else if (mounted && groupSessionProvider.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(groupSessionProvider.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Group Walkabouts'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          Consumer<GroupSessionProvider>(
            builder: (context, groupSessionProvider, child) {
              return IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: groupSessionProvider.isLoading ? null : _loadActiveSessions,
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadActiveSessions,
        child: _buildBody(),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Consumer<GroupSessionProvider>(
          builder: (context, groupSessionProvider, child) {
            return FloatingActionButton.extended(
              onPressed: groupSessionProvider.isLoading ? null : _joinSession,
              label: const Text('Join Session'),
              icon: const Icon(Icons.qr_code_scanner),
              backgroundColor: AppColors.secondary,
              heroTag: 'join',
            );
          },
        ),
        const SizedBox(height: 16),
        Consumer<GroupSessionProvider>(
          builder: (context, groupSessionProvider, child) {
            return FloatingActionButton.extended(
              onPressed: groupSessionProvider.isLoading ? null : _createNewSession,
              label: const Text('Create Session'),
              icon: const Icon(Icons.add),
              backgroundColor: AppColors.primary,
              heroTag: 'create',
            );
          },
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Consumer<GroupSessionProvider>(
      builder: (context, groupSessionProvider, child) {
        if (groupSessionProvider.isLoading && groupSessionProvider.activeSessions.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (groupSessionProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[300],
                ),
                const SizedBox(height: 16),
                Text(
                  groupSessionProvider.error!,
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadActiveSessions,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (groupSessionProvider.activeSessions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.group_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No active group sessions',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Create a new session or join an existing one',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: groupSessionProvider.activeSessions.length,
          itemBuilder: (context, index) {
            final session = groupSessionProvider.activeSessions[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: GroupSessionCard(
                session: session,
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/session-management',
                    arguments: session.id,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}

/// Dialog for joining a session by invite code
class _JoinSessionDialog extends StatefulWidget {
  @override
  State<_JoinSessionDialog> createState() => _JoinSessionDialogState();
}

class _JoinSessionDialogState extends State<_JoinSessionDialog> {
  final TextEditingController _codeController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Join Session'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter the invite code to join a group walkabout session.',
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Invite Code',
                hintText: 'Enter 6-digit code',
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.characters,
              maxLength: 6,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an invite code';
                }
                if (value.length != 6) {
                  return 'Invite code must be 6 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop(_codeController.text.toUpperCase());
            }
          },
          child: const Text('Join'),
        ),
      ],
    );
  }
}