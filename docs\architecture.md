# SafeStride Architecture Document

## Introduction

This document outlines the overall project architecture for SafeStride, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
Since SafeStride includes a significant user interface (Flutter mobile app), a separate Frontend Architecture Document will detail the frontend-specific design and MUST be used in conjunction with this document. Core technology stack choices documented herein are definitive for the entire project.

### Starter Template or Existing Project

SafeStride is a greenfield Flutter application leveraging:

- Flutter's standard project structure
- Firebase's recommended integration patterns
- Cross-platform mobile development best practices

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-28 | 1.0 | Initial architecture document based on PRD v3.0 | Winston (Architect) |

## High Level Architecture

### Technical Summary

SafeStride employs a **serverless mobile-first architecture** with Flutter providing cross-platform mobile capabilities and Google Firebase delivering backend services. The system prioritizes offline-first functionality through SQLite local caching with Firebase Firestore synchronization. Core architectural patterns include Repository pattern for data access, Provider pattern for state management, and Event-driven communication for real-time collaboration features.

### High Level Overview

**Architectural Style:** Serverless with offline-first mobile client
**Repository Structure:** Monorepo (Flutter single codebase for iOS/Android)
**Service Architecture:** Google Firebase serverless backend

**Primary User Flow:**

1. User authenticates via Firebase Auth (email/SSO)
2. Role selection (Leader/Observer) determines available features
3. Offline-capable safety inspections with SQLite storage
4. Background sync to Firestore when connectivity available
5. Real-time collaboration through Firestore listeners
6. AI-powered hazard detection using on-device TensorFlow Lite

### Architectural Patterns

- **Serverless Architecture:** Firebase Functions + Firestore - *Rationale:* Eliminates infrastructure management, automatic scaling, cost-effective for variable workloads
- **Offline-First Pattern:** SQLite + Firestore sync - *Rationale:* Critical for industrial environments with poor connectivity
- **Repository Pattern:** Abstract data access layer - *Rationale:* Enables testing, supports offline/online data source switching
- **Provider Pattern:** Flutter state management - *Rationale:* Reactive UI updates, clean separation of business logic
- **Event-Driven Communication:** Firestore real-time listeners - *Rationale:* Enables collaborative features without polling

## Tech Stack

### Cloud Infrastructure

- **Provider:** Google Cloud Platform (via Firebase)
- **Key Services:** Firebase Auth, Firestore, Cloud Functions, Cloud Storage, Analytics
- **Deployment Regions:** Multi-region (automatic via Firebase)

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|----------|
| **Mobile Framework** | Flutter | 3.16.0 | Cross-platform mobile development | Single codebase, native performance, strong ecosystem |
| **Language** | Dart | 3.2.0 | Primary development language | Optimized for Flutter, strong typing, null safety |
| **Backend** | Firebase | Latest | Serverless backend services | Integrated ecosystem, real-time capabilities, offline support |
| **Authentication** | Firebase Auth | Latest | User authentication & SSO | Built-in providers, secure, integrates with Firestore rules |
| **Database** | Cloud Firestore | Latest | Primary cloud database | Real-time sync, offline support, scalable NoSQL |
| **Local Storage** | SQLite (sqflite) | 2.3.0 | Offline data caching | Reliable local storage, SQL queries, Flutter integration |
| **AI/ML** | TensorFlow Lite | 0.10.0 | On-device AI inference | Offline capability, optimized for mobile, privacy-preserving |
| **Image Processing** | image_picker | 1.0.4 | Photo capture & compression | Native camera integration, image optimization |
| **QR Codes** | qr_flutter, mobile_scanner | 4.1.0, 5.2.3 | QR generation & scanning | Team collaboration features |
| **PDF Generation** | pdf | 3.10.4 | Report generation | Compliance reporting requirements |
| **State Management** | Provider | 6.1.1 | Application state management | Simple, performant, Flutter-recommended |
| **Notifications** | flutter_local_notifications | 16.3.0 | Local push notifications | Task reminders, offline notifications |

## Data Models

### User

**Purpose:** Represents system users with role-based access

**Key Attributes:**

- id: String - Unique Firebase Auth UID
- email: String - User email address
- name: String - Display name
- role: Enum (Leader, Observer) - Determines feature access
- subscription: Enum (Free, Premium) - Subscription tier
- createdAt: DateTime - Account creation timestamp

**Relationships:**

- One-to-many with Sessions (as creator or participant)
- One-to-many with Findings (as author)

### Site

**Purpose:** Represents physical locations for safety inspections

**Key Attributes:**

- id: String - Unique identifier
- name: String - Site display name
- ownerId: String - Reference to User
- areas: List<String> - References to Area IDs
- createdAt: DateTime - Creation timestamp

**Relationships:**

- Belongs to User (owner)
- One-to-many with Areas

### Area

**Purpose:** Specific zones within sites for targeted inspections

**Key Attributes:**

- id: String - Unique identifier
- name: String - Area display name
- siteId: String - Reference to parent Site
- description: String - Optional area description

### Session

**Purpose:** Individual or collaborative safety inspection instances

**Key Attributes:**

- id: String - Unique identifier
- type: Enum (Solo, Group) - Session type
- leaderId: String - Reference to leading User
- areaId: String - Reference to inspection Area
- status: Enum (Active, Completed, Cancelled) - Current state
- participants: List<String> - Observer User IDs
- inviteCode: String - QR code/link for joining
- createdAt: DateTime - Session start time
- completedAt: DateTime? - Session completion time

**Relationships:**

- Belongs to User (leader)
- Belongs to Area
- One-to-many with Findings
- Many-to-many with Users (participants)

### Finding

**Purpose:** Individual safety hazards or observations

**Key Attributes:**

- id: String - Unique identifier
- sessionId: String - Reference to parent Session
- authorId: String - Reference to User who created
- description: String - Hazard description
- severity: Enum (Low, Medium, High) - Risk level
- category: String - AI-suggested or manual category
- photoUrl: String? - Reference to stored image
- location: GeoPoint? - GPS coordinates if available
- status: Enum (Open, InProgress, Resolved) - Resolution state
- assignedTo: String? - User ID for follow-up
- createdAt: DateTime - Finding timestamp
- resolvedAt: DateTime? - Resolution timestamp

**Relationships:**

- Belongs to Session
- Belongs to User (author)
- May reference User (assignedTo)

## Components

### Mobile Application (Flutter)

**Responsibility:** Primary user interface and offline functionality

**Key Modules:**

- Authentication Module (Firebase Auth integration)
- Role Management (Leader/Observer workflows)
- Inspection Module (checklist, hazard documentation)
- Collaboration Module (QR codes, real-time sync)
- AI Module (TensorFlow Lite hazard detection)
- Offline Storage (SQLite repository layer)
- Sync Engine (Firestore synchronization)

**Interfaces:**

- Firebase Authentication API
- Firestore Database API
- Device camera and storage APIs
- Local SQLite database

### Firebase Backend

**Responsibility:** Serverless backend services and data management

**Key Services:**

- Firebase Authentication (user management, SSO)
- Cloud Firestore (real-time database, offline sync)
- Cloud Storage (photo/document storage)
- Cloud Functions (business logic, data processing)
- Firebase Analytics (usage tracking)

**Security Rules:**

- Role-based access control
- Data isolation between organizations
- Read/write permissions based on user roles

### AI/ML Pipeline

**Responsibility:** On-device hazard detection and categorization

**Components:**

- MobileNetV2 model (<4MB) for image classification
- MiniLM model (<5MB) for text similarity/duplicate detection
- Image preprocessing pipeline
- Confidence scoring and fallback mechanisms

**Interfaces:**

- TensorFlow Lite runtime
- Device camera input
- Local model storage

## External APIs

### Firebase Services

**Purpose:** Primary backend infrastructure
**Authentication:** Firebase Admin SDK, API keys
**Key Endpoints:**

- Authentication: Firebase Auth REST API
- Database: Firestore REST API
- Storage: Cloud Storage API
**Rate Limits:** Firebase quotas (Spark: 50K reads/day, Blaze: pay-per-use)

### Device APIs

**Purpose:** Native mobile capabilities
**Key Integrations:**

- Camera API (photo capture)
- File System API (local storage)
- Network API (connectivity detection)
- Notification API (local notifications)

## Core Workflows

### Solo Inspection Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant S as SQLite
    participant F as Firestore
    
    U->>A: Start Solo Walkabout
    A->>S: Create local session
    U->>A: Complete checklist items
    A->>S: Store findings locally
    U->>A: Add hazard photos
    A->>S: Cache compressed images
    A->>A: Run AI hazard detection
    U->>A: Complete inspection
    A->>F: Sync session data (when online)
    A->>F: Upload photos to Cloud Storage
    F-->>A: Confirm sync completion
```

### Group Collaboration Workflow

```mermaid
sequenceDiagram
    participant L as Leader
    participant O as Observer
    participant A as App
    participant F as Firestore
    
    L->>A: Create Group Session
    A->>F: Store session with invite code
    L->>O: Share QR code/link
    O->>A: Scan QR code
    A->>F: Request to join session
    F-->>A: Notify Leader of join request
    L->>A: Approve Observer
    A->>F: Update session participants
    F-->>A: Real-time sync to Observer
    O->>A: Submit findings
    A->>F: Store findings with real-time updates
    F-->>A: Sync to all participants
    L->>A: Review and merge findings
    A->>F: Finalize session
```

## Database Schema

### Firestore Collections

```
/users/{userId}
  - email: string
  - name: string
  - role: string
  - subscription: string
  - createdAt: timestamp

/sites/{siteId}
  - name: string
  - ownerId: string
  - areas: array<string>
  - createdAt: timestamp

/areas/{areaId}
  - name: string
  - siteId: string
  - description: string

/sessions/{sessionId}
  - type: string
  - leaderId: string
  - areaId: string
  - status: string
  - participants: array<string>
  - inviteCode: string
  - createdAt: timestamp
  - completedAt: timestamp?

/sessions/{sessionId}/findings/{findingId}
  - description: string
  - severity: string
  - category: string
  - photoUrl: string?
  - authorId: string
  - status: string
  - assignedTo: string?
  - createdAt: timestamp
  - resolvedAt: timestamp?
```

### SQLite Schema (Local Cache)

```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  role TEXT,
  subscription TEXT,
  synced INTEGER DEFAULT 0
);

CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  leader_id TEXT,
  area_id TEXT,
  status TEXT,
  invite_code TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0
);

CREATE TABLE findings (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  description TEXT,
  severity TEXT,
  category TEXT,
  photo_path TEXT,
  author_id TEXT,
  status TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (session_id) REFERENCES sessions(id)
);
```

## Infrastructure and Deployment

### Infrastructure as Code

**Approach:** Firebase configuration files and CLI deployment
**Key Files:**

- `firebase.json` - Project configuration
- `firestore.rules` - Security rules
- `firestore.indexes.json` - Database indexes
- `storage.rules` - File storage security

### Deployment Strategy

**Mobile App:**

- iOS: App Store Connect via Xcode Cloud or manual upload
- Android: Google Play Console via automated CI/CD
- Code signing and provisioning profiles managed securely

**Backend:**

- Firebase services deployed via Firebase CLI
- Automated deployment through GitHub Actions
- Environment-specific configurations (dev/staging/prod)

### Environments

1. **Development:** Local Firebase emulator suite
2. **Staging:** Dedicated Firebase project for testing
3. **Production:** Production Firebase project with monitoring

### Promotion Flow

1. Development → Feature branch testing
2. Staging → Integration testing with real Firebase services
3. Production → Gradual rollout via app store mechanisms

### Rollback Strategy

- Mobile: Previous app version available in stores
- Backend: Firebase project restore from daily backups
- Database: Firestore automatic point-in-time recovery

## Error Handling Strategy

### General Approach

- **Graceful Degradation:** Core functionality works offline
- **User-Friendly Messages:** Clear, actionable error communication
- **Automatic Recovery:** Background retry mechanisms for sync failures
- **Logging:** Comprehensive error tracking via Firebase Crashlytics

### Logging Standards

- **Error Levels:** Debug, Info, Warning, Error, Fatal
- **Structured Logging:** JSON format with consistent fields
- **Privacy:** No sensitive data in logs (PII, passwords)
- **Retention:** 30 days for debug logs, 1 year for error logs

### Error Patterns

**External API Failures:**

- Network timeouts: Retry with exponential backoff
- Authentication errors: Prompt for re-login
- Rate limiting: Queue requests with user notification

**Business Logic Errors:**

- Validation failures: Immediate user feedback with correction guidance
- State conflicts: Automatic resolution or user choice prompts
- Data inconsistencies: Background sync with conflict resolution

**Data Consistency:**

- Offline/online sync conflicts: Timestamp-based resolution
- Concurrent edits: Last-write-wins with user notification
- Data corruption: Local backup restoration

## Coding Standards

### Core Standards

- **Dart Style Guide:** Official Dart formatting and conventions
- **Null Safety:** Strict null safety enabled
- **Documentation:** Comprehensive dartdoc comments for public APIs
- **Testing:** Minimum 80% code coverage for business logic

### Naming Conventions

- **Classes:** PascalCase (UserRepository, SessionService)
- **Variables/Functions:** camelCase (getCurrentUser, sessionId)
- **Constants:** SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE)
- **Files:** snake_case (user_repository.dart, session_service.dart)

### Critical Rules

1. **No direct Firebase calls in UI:** Use repository pattern
2. **Always handle offline state:** Check connectivity before network calls
3. **Validate all user inputs:** Client and server-side validation
4. **Encrypt sensitive data:** Use secure storage for credentials
5. **Log errors appropriately:** Include context but protect privacy

## Test Strategy

### Philosophy

**Test Pyramid:** Unit tests (70%) > Integration tests (20%) > E2E tests (10%)
**Offline-First Testing:** All core features must work without network
**Device Testing:** Test on minimum supported devices (iPhone 7, Android 8.0)

### Test Types

**Unit Tests:**

- Business logic and data models
- Repository implementations
- AI model integration
- Utility functions and helpers

**Integration Tests:**

- Firebase service integration
- SQLite database operations
- Photo capture and compression
- Sync engine functionality

**End-to-End Tests:**

- Complete user workflows (login → inspection → sync)
- Cross-platform compatibility
- Offline/online transition scenarios
- Collaboration features

### Data Management

- **Test Data:** Isolated test Firebase project
- **Mocking:** Mock external dependencies for unit tests
- **Cleanup:** Automated test data cleanup after runs

### Continuous Testing

- **CI/CD Integration:** Tests run on every commit
- **Device Farm:** Automated testing on real devices
- **Performance Testing:** Monitor app startup and sync times

## Security

### Input Validation

- **Client-Side:** Immediate feedback and basic validation
- **Server-Side:** Comprehensive validation via Firestore rules
- **Sanitization:** Clean all user inputs before storage
- **File Upload:** Validate image types and sizes

### Authentication & Authorization

- **Multi-Factor Auth:** Available for enhanced security
- **Role-Based Access:** Leader/Observer permissions enforced
- **Session Management:** Automatic timeout and refresh
- **SSO Integration:** Google support

### Secrets Management

- **API Keys:** Environment variables and secure storage
- **Certificates:** Secure keychain storage on devices
- **Database Credentials:** Firebase handles automatically
- **Encryption Keys:** Device-specific secure storage

### API Security

- **HTTPS Only:** All network communication encrypted
- **Rate Limiting:** Firebase quotas and custom limits
- **Input Validation:** Comprehensive server-side checks
- **CORS:** Properly configured for web admin interfaces

### Data Protection

- **Encryption at Rest:** Firestore and device storage encrypted
- **Encryption in Transit:** TLS 1.3 for all communications
- **Data Minimization:** Collect only necessary information
- **Right to Deletion:** User data removal capabilities

### Dependency Security

- **Vulnerability Scanning:** Regular dependency audits
- **Update Strategy:** Timely security patch application
- **Supply Chain:** Verify package integrity and sources
- **License Compliance:** Track and approve all dependencies

### Security Testing

- **Penetration Testing:** Annual third-party security assessment
- **Vulnerability Scanning:** Automated dependency scanning
- **Code Analysis:** Static security analysis tools
- **Privacy Audit:** Regular privacy compliance reviews

## Next Steps

### Immediate Actions

1. **Frontend Architecture Document:** Create detailed Flutter app architecture
2. **Development Environment Setup:** Configure Firebase projects and development tools
3. **Repository Structure:** Establish Flutter project structure and CI/CD pipelines
4. **Security Configuration:** Implement Firestore rules and authentication flows

### Development Phases

1. **Phase 1:** Core authentication and basic inspection functionality
2. **Phase 2:** Collaboration features and AI integration
3. **Phase 3:** Advanced reporting and premium features
4. **Phase 4:** Performance optimization and scaling

### Frontend Architecture Prompt

Using this SafeStride architecture document as foundation, create a detailed Flutter frontend architecture focusing on:

- Offline-first state management patterns
- Role-based UI navigation and feature access
- Camera integration and image processing workflows
- Real-time collaboration UI patterns
- Accessibility compliance for industrial users
- Performance optimization for older devices (iPhone 7+, Android 8.0+)

---

*This architecture document serves as the definitive technical blueprint for SafeStride development. All implementation decisions should align with the patterns, technologies, and principles outlined herein.*
