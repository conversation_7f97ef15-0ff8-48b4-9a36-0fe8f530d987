import 'package:flutter/foundation.dart';
import '../models/session_model.dart';
import '../models/user_model.dart';
import '../services/group_session_service.dart';
import '../services/invitation_service.dart';
import '../repositories/repository_factory.dart';

/// Provider for managing group session state
class GroupSessionProvider extends ChangeNotifier {
  final GroupSessionService _groupSessionService;
  final InvitationService _invitationService;

  List<SessionModel> _activeSessions = [];
  bool _isLoading = false;
  String? _error;

  GroupSessionProvider()
      : _invitationService = InvitationService(),
        _groupSessionService = GroupSessionService(
          sessionRepository: RepositoryFactory().getSessionRepository(),
          invitationService: InvitationService(),
        );

  // Getters
  List<SessionModel> get activeSessions => List.unmodifiable(_activeSessions);
  bool get isLoading => _isLoading;
  String? get error => _error;
  GroupSessionService get groupSessionService => _groupSessionService;
  InvitationService get invitationService => _invitationService;

  /// Load active group sessions for a leader
  Future<void> loadActiveSessions(String leaderId) async {
    _setLoading(true);
    _clearError();

    try {
      final sessions = await _groupSessionService.getLeaderGroupSessions(leaderId);
      _activeSessions = sessions.where((s) => s.status == SessionStatus.active).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sessions: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new group session
  Future<SessionModel?> createSession({
    required String leaderId,
    required String areaId,
    required int maxParticipants,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final session = await _groupSessionService.createGroupSession(
        leaderId: leaderId,
        areaId: areaId,
        maxParticipants: maxParticipants,
      );
      
      _activeSessions.add(session);
      notifyListeners();
      return session;
    } catch (e) {
      _setError('Failed to create session: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Join a session using invite code
  Future<SessionModel?> joinSession({
    required String sessionId,
    required String participantId,
    required String inviteCode,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final session = await _groupSessionService.addParticipant(
        sessionId: sessionId,
        participantId: participantId,
      );
      
      // Update local sessions if this user is the leader
      final index = _activeSessions.indexWhere((s) => s.id == sessionId);
      if (index != -1) {
        _activeSessions[index] = session;
        notifyListeners();
      }
      
      return session;
    } catch (e) {
      _setError('Failed to join session: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<SessionModel?> joinSessionByInviteCode({
    required String inviteCode,
    required String observerId,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final session = await _groupSessionService.joinSessionByInviteCode(
        inviteCode: inviteCode,
        observerId: observerId,
      );
      
      return session;
    } catch (e) {
      _setError('Failed to join session: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Complete a session
  Future<bool> completeSession(String sessionId) async {
    _setLoading(true);
    _clearError();

    try {
      final session = await _groupSessionService.completeSession(sessionId);
      
      // Update local session
      final index = _activeSessions.indexWhere((s) => s.id == sessionId);
      if (index != -1) {
        _activeSessions[index] = session;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('Failed to complete session: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Cancel a session
  Future<bool> cancelSession(String sessionId) async {
    _setLoading(true);
    _clearError();

    try {
      final session = await _groupSessionService.cancelSession(sessionId);
      
      // Update local session
      final index = _activeSessions.indexWhere((s) => s.id == sessionId);
      if (index != -1) {
        _activeSessions[index] = session;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('Failed to cancel session: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh sessions
  Future<void> refreshSessions(String leaderId) async {
    await loadActiveSessions(leaderId);
  }

  /// Clear error
  void clearError() {
    _clearError();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
}