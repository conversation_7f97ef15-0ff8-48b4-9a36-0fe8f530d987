<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>873787664908-8cnniseoubuaoqk5llakuq11s0rrk0se.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.873787664908-8cnniseoubuaoqk5llakuq11s0rrk0se</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>873787664908-i8om33mv954odui727i0f40ki97ueai6.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyAJ0AwLJbfrLlBPf0_LZ6cGSajT1MNyCZk</string>
	<key>GCM_SENDER_ID</key>
	<string>873787664908</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.melur.safestride</string>
	<key>PROJECT_ID</key>
	<string>safestride-bd853</string>
	<key>STORAGE_BUCKET</key>
	<string>safestride-bd853.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:873787664908:ios:697f557d76e010be121c29</string>
</dict>
</plist>