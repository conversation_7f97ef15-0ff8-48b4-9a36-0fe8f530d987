# Technical Assumptions

## Repository Structure
Monorepo, as Flutter’s single codebase supports both iOS and Android, with Firebase services managed in a unified project structure.

## Service Architecture
Serverless architecture using Google Firebase (Authentication, Firestore, Analytics, Hosting) for scalable backend services, with SQLite for local offline caching. This aligns with the need for offline-first functionality and enterprise scalability.

## Testing Requirements
- Unit tests for core logic (e.g., AI tagging, data sync).
- Integration tests for Firebase Authentication, Firestore sync, and SQLite caching.
- End-to-end (E2E) tests for key user flows (login, role selection, walkabout creation, report generation).
- Manual testing convenience methods for QR code scanning and AI model accuracy validation in industrial settings.

## Additional Technical Assumptions and Requests
- **Flutter**: Chosen for cross-platform development to reduce effort for a single developer, supporting iOS and Android with one codebase.
- **Google Firebase**: Selected for authentication (email/password, SSO), Firestore for data storage/sync, Analytics for usage tracking, and Hosting for scalability, aligning with offline-first and enterprise needs.
- **Plugins**: `image_picker` for photo capture (250KB compression), `tflite_flutter` for AI (MobileNetV2, MiniLM), `mobile_scanner` and `qr_flutter` for QR code functionality, `share_plus` for invitation links, `pdf` for report generation, `googleapis` for cloud storage, `in_app_purchase` and `stripe_payment` for monetization, `flutter_local_notifications` for task alerts.
- **Security**: HTTPS for cloud sync, encrypted SQLite, and Firestore rules ensure data privacy (e.g., role-based access).
- **Performance**: Optimized for <1-second UI actions and 100 sessions/month, with photo compression to manage storage (100MB Free, 1TB Premium).

## Backup, Recovery & Maintenance

### Data Backup Strategy
- **Local Backup**: Critical inspection data (findings, photos, checklists) automatically backed up to device storage every 24 hours
- **Cloud Backup**: All user data synced to Firebase Firestore with automatic daily backups to Google Cloud Storage
- **Incremental Backup**: Only changed data backed up to minimize storage usage and sync time
- **Backup Retention**: Local backups retained for 30 days, cloud backups retained for 1 year (Free) / 3 years (Premium)

### Recovery Procedures
- **Device Loss/Replacement**: Users can restore all data by logging in on new device (cloud sync)
- **Data Corruption**: Local backup automatically restored if corruption detected during app startup
- **Sync Conflicts**: Timestamp-based conflict resolution with user notification for manual review if needed
- **Account Recovery**: Password reset and account recovery through Firebase Authentication

### Maintenance & Support
- **App Updates**: Over-the-air updates via app stores with backward compatibility for data formats
- **Database Maintenance**: Automated cleanup of expired data (completed follow-ups >1 year old)
- **Performance Monitoring**: Firebase Analytics tracks app performance and crash reports
- **User Support**: In-app help documentation and contact form for technical support

## Privacy & Data Protection

### Data Collection & Usage
- **Personal Data**: Email, name, role, and device ID collected for authentication and app functionality
- **Inspection Data**: Photos, descriptions, locations, and timestamps stored for safety documentation purposes
- **Usage Analytics**: Anonymized usage patterns collected to improve app performance and features
- **No Third-Party Sharing**: User data never shared with third parties except as required by law

### Data Retention & Deletion
- **Active Data**: Inspection findings retained indefinitely while user account is active
- **Inactive Accounts**: Data automatically deleted after 2 years of account inactivity
- **User-Requested Deletion**: Complete data deletion within 30 days of user request
- **Legal Compliance**: Data retained as required by workplace safety regulations (typically 3-7 years)

### User Consent & Control
- **Initial Consent**: Users must accept privacy policy and terms of service during account creation
- **Data Access**: Users can download all their data in standard formats (CSV, PDF) at any time
- **Granular Controls**: Users can disable analytics collection and photo location tagging in settings
- **Consent Withdrawal**: Users can request account deletion and data removal at any time

### Security Measures
- **Encryption**: All data encrypted in transit (HTTPS) and at rest (encrypted SQLite, Firestore)
- **Access Control**: Role-based access ensures users only see data they're authorized to view
- **Authentication**: Multi-factor authentication available for enhanced security
- **Audit Trail**: All data access and modifications logged for security monitoring
