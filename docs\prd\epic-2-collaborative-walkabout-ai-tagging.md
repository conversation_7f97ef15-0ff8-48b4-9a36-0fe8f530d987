# Epic 2 Collaborative Walkabout & AI Tagging
Enable Group Walkabout with QR code and invite link features, integrate AI hazard tagging, and support Observer collaboration to streamline team-based inspections.

## Story 2.1 [High] Group Walkabout Setup & Invitations
As a Leader, I want to coordinate team-based safety inspections, so that multiple team members can collaborate efficiently on comprehensive safety reviews.

### Acceptance Criteria
- 2.1.1: Home screen offers “Group Walkabout” option for Leaders.
- 2.1.2: Session creation includes Area selection and team setup (max 2 Observers in Free tier, unlimited in Premium).
- 2.1.3: Leaders generate invitation links (WhatsApp, SMS, email via `share_plus`) or QR codes (`qr_flutter`) for Observers.
- 2.1.4: Review Hub displays Observer join requests for <PERSON> to accept/reject.
- 2.1.5: Session data stored in SQLite/Firestore with `invite_link` and `qr_code` fields.
- 2.1.6: E2E tests for session creation and invitation flows.

## Story 2.2 [High] AI Model Infrastructure Setup
As a developer, I want to establish AI model infrastructure and dependencies, so that hazard auto-tagging features can be reliably implemented and maintained.

### Acceptance Criteria
- 2.2.1: TensorFlow Lite Flutter plugin (`tflite_flutter` v0.10.0) integrated with proper Android/iOS native dependencies.
- 2.2.2: AI model storage directory structure created in app documents (`/models/mobilenet_v2/`, `/models/minilm/`).
- 2.2.3: Model metadata management system implemented with version checking and automatic updates via Firebase Storage.
- 2.2.4: MobileNetV2 model (<4MB) and MiniLM model (<5MB) downloaded and validated during app initialization.
- 2.2.5: AI model loading and inference pipeline established with error handling and graceful degradation.
- 2.2.6: Model performance benchmarking on target devices (inference time <500ms, memory usage <50MB).
- 2.2.7: Unit tests for model loading, inference pipeline, and fallback mechanisms.
- 2.2.8: Integration tests for model download, validation, and update workflows.

## Story 2.3 [High] Observer Joining & Submission
As an Observer, I want to participate in team safety inspections, so that I can contribute my expertise to comprehensive safety assessments.

### Acceptance Criteria
- 2.3.1: Session Join screen allows QR code scanning (`mobile_scanner`) to request joining a session.
- 2.3.2: Observers submit checklist sections (Pass/Fail, notes, photos) and hazards (description, severity, photo).
- 2.3.3: Findings cached in SQLite, synced to Firestore, with sync status UI.
- 2.3.4: Observers receive notifications (`flutter_local_notifications`) for acceptance/rejection.
- 2.3.5: E2E tests for joining and submission flows.

## Story 2.4 [High] AI Hazard Auto-Tagging
As a user, I want automated assistance in categorizing safety hazards, so that I can document findings more quickly and consistently.

### Acceptance Criteria
- 2.4.1: MobileNetV2 model suggests hazard tags offline using established AI infrastructure (Free: 3 tags/session, 5 categories; Premium: unlimited, 9-10 categories).
- 2.4.2: Categories include Spill, Blocked Exit, Electrical Issue, Trip Hazard, Fire Hazard (Free); adds Chemical Spill, Clutter, Poor Lighting, Ergonomic Issue (Premium).
- 2.4.3: Image preprocessing pipeline optimizes photos for AI inference (resize, normalize, format conversion).
- 2.4.4: Confidence scoring system filters low-quality suggestions (minimum 70% confidence threshold).
- 2.4.5: Users confirm/edit AI-suggested tags before submission, stored in SQLite/Firestore with confidence scores.
- 2.4.6: Fallback to manual tagging when AI models unavailable or inference fails.
- 2.4.7: Unit tests for AI model accuracy and tag relevance; manual tests with industrial safety image dataset.
