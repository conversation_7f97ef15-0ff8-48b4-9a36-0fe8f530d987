import 'package:uuid/uuid.dart';
import '../models/join_request.dart';
import '../repositories/session_repository.dart';
import '../services/database_service.dart';
import '../services/firestore_service.dart';

/// Service for managing join requests for group walkabouts
class JoinRequestService {
  final DatabaseService _databaseService;
  final FirestoreService _firestoreService;
  final SessionRepository _sessionRepository;
  final Uuid _uuid = const Uuid();

  JoinRequestService({
    required DatabaseService databaseService,
    required FirestoreService firestoreService,
    required SessionRepository sessionRepository,
  })  : _databaseService = databaseService,
        _firestoreService = firestoreService,
        _sessionRepository = sessionRepository;

  /// Create a new join request
  Future<JoinRequest> createJoinRequest({
    required String sessionId,
    required String observerId,
  }) async {
    // Check if session exists and is active
    final session = await _sessionRepository.getSession(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    if (session.status != SessionStatus.active) {
      throw Exception('Session is not active');
    }

    // Check if observer is already a participant
    if (session.participants.contains(observerId)) {
      throw Exception('Observer is already a participant');
    }

    // Check if there's already a pending request
    final existingRequest = await getJoinRequestByObserver(
      sessionId: sessionId,
      observerId: observerId,
    );
    
    if (existingRequest != null && existingRequest.status == JoinRequestStatus.pending) {
      throw Exception('Join request already pending');
    }

    // Check if session is full
    if (session.participants.length >= session.maxParticipants) {
      throw Exception('Session is full');
    }

    final joinRequest = JoinRequest(
      id: _uuid.v4(),
      sessionId: sessionId,
      observerId: observerId,
      status: JoinRequestStatus.pending,
      createdAt: DateTime.now(),
    );

    // Store locally
    await _storeJoinRequestLocally(joinRequest);
    
    // Sync to Firestore
    try {
      await _syncJoinRequestToFirestore(joinRequest);
      await _markJoinRequestSynced(joinRequest.id);
    } catch (e) {
      // Continue even if sync fails - will retry later
    }

    return joinRequest;
  }

  /// Accept a join request
  Future<JoinRequest> acceptJoinRequest(String requestId) async {
    final request = await getJoinRequest(requestId);
    if (request == null) {
      throw Exception('Join request not found');
    }

    if (request.status != JoinRequestStatus.pending) {
      throw Exception('Join request is not pending');
    }

    // Check session capacity again
    final session = await _sessionRepository.getSession(request.sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    if (session.participants.length >= session.maxParticipants) {
      throw Exception('Session is full');
    }

    // Update request status
    final updatedRequest = request.copyWith(
      status: JoinRequestStatus.accepted,
    );

    await _updateJoinRequestLocally(updatedRequest);
    
    // Add participant to session
    final updatedParticipants = [...session.participants, request.observerId];
    final updatedSession = session.copyWith(participants: updatedParticipants);
    await _sessionRepository.updateSession(updatedSession);

    // Sync to Firestore
    try {
      await _syncJoinRequestToFirestore(updatedRequest);
      await _markJoinRequestSynced(updatedRequest.id);
    } catch (e) {
      // Continue even if sync fails - will retry later
    }

    return updatedRequest;
  }

  /// Reject a join request
  Future<JoinRequest> rejectJoinRequest(String requestId) async {
    final request = await getJoinRequest(requestId);
    if (request == null) {
      throw Exception('Join request not found');
    }

    if (request.status != JoinRequestStatus.pending) {
      throw Exception('Join request is not pending');
    }

    final updatedRequest = request.copyWith(
      status: JoinRequestStatus.rejected,
    );

    await _updateJoinRequestLocally(updatedRequest);
    
    // Sync to Firestore
    try {
      await _syncJoinRequestToFirestore(updatedRequest);
      await _markJoinRequestSynced(updatedRequest.id);
    } catch (e) {
      // Continue even if sync fails - will retry later
    }

    return updatedRequest;
  }

  /// Get join request by ID
  Future<JoinRequest?> getJoinRequest(String requestId) async {
    final db = await _databaseService.database;
    final result = await db.query(
      'join_requests',
      where: 'id = ?',
      whereArgs: [requestId],
    );

    if (result.isNotEmpty) {
      return JoinRequest.fromMap(result.first);
    }
    return null;
  }

  /// Get join request by observer for a specific session
  Future<JoinRequest?> getJoinRequestByObserver({
    required String sessionId,
    required String observerId,
  }) async {
    final db = await _databaseService.database;
    final result = await db.query(
      'join_requests',
      where: 'session_id = ? AND observer_id = ?',
      whereArgs: [sessionId, observerId],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return JoinRequest.fromMap(result.first);
    }
    return null;
  }

  /// Get all pending join requests for a session
  Future<List<JoinRequest>> getPendingJoinRequests(String sessionId) async {
    final db = await _databaseService.database;
    final result = await db.query(
      'join_requests',
      where: 'session_id = ? AND status = ?',
      whereArgs: [sessionId, JoinRequestStatus.pending.name],
      orderBy: 'created_at ASC',
    );

    return result.map((map) => JoinRequest.fromMap(map)).toList();
  }

  /// Get all join requests for a session
  Future<List<JoinRequest>> getJoinRequestsForSession(String sessionId) async {
    final db = await _databaseService.database;
    final result = await db.query(
      'join_requests',
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'created_at DESC',
    );

    return result.map((map) => JoinRequest.fromMap(map)).toList();
  }

  /// Store join request locally
  Future<void> _storeJoinRequestLocally(JoinRequest request) async {
    final db = await _databaseService.database;
    await db.insert('join_requests', request.toMap());
  }

  /// Update join request locally
  Future<void> _updateJoinRequestLocally(JoinRequest request) async {
    final db = await _databaseService.database;
    await db.update(
      'join_requests',
      request.toMap(),
      where: 'id = ?',
      whereArgs: [request.id],
    );
  }

  /// Sync join request to Firestore
  Future<void> _syncJoinRequestToFirestore(JoinRequest request) async {
    await _firestoreService.setDocument(
      'sessions/${request.sessionId}/joinRequests/${request.id}',
      {
        'observerId': request.observerId,
        'status': request.status.name,
        'createdAt': request.createdAt,
      },
    );
  }

  /// Mark join request as synced
  Future<void> _markJoinRequestSynced(String requestId) async {
    final db = await _databaseService.database;
    await db.update(
      'join_requests',
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [requestId],
    );
  }

  /// Sync unsynced join requests to Firestore
  Future<void> syncUnsyncedJoinRequests() async {
    final db = await _databaseService.database;
    final result = await db.query(
      'join_requests',
      where: 'synced = ?',
      whereArgs: [0],
    );

    for (final map in result) {
      try {
        final request = JoinRequest.fromMap(map);
        await _syncJoinRequestToFirestore(request);
        await _markJoinRequestSynced(request.id);
      } catch (e) {
        // Continue with next request if one fails
        continue;
      }
    }
  }
}