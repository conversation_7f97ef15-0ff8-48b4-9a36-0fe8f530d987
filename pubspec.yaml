name: safestride
description: "SafeStride - Industrial Safety Inspection App"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.2.0
  flutter: ">=3.16.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8

  # Firebase Services
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_analytics: ^10.7.4

  # Local Storage
  sqflite: ^2.3.0
  path: ^1.8.3

  # State Management
  provider: ^6.1.1

  # Image Processing
  image_picker: ^1.0.4
  path_provider: ^2.1.1

  # File Sharing
  share_plus: ^7.2.1

  # QR Codes
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.2.3

  # PDF Generation
  pdf: ^3.10.4

  # Notifications
  flutter_local_notifications: ^19.3.0

  # Connectivity
  connectivity_plus: ^5.0.2

  # Secure Storage
  flutter_secure_storage: ^9.0.0

  # HTTP
  http: ^1.1.2

  # Utilities
  uuid: ^4.2.1
  intl: ^0.19.0
  crypto: ^3.0.6
  google_sign_in: ^7.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0

  # Testing
  mockito: ^5.4.4
  build_runner: ^2.4.7
  fake_cloud_firestore: ^2.4.2
  sqflite_common_ffi: ^2.3.0
  integration_test:
    sdk: flutter
  firebase_auth_mocks: ^0.13.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
