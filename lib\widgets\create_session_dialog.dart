import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Dialog for creating a new group walkabout session
class CreateSessionDialog extends StatefulWidget {
  final int maxParticipants;
  final List<String>? availableAreas;

  const CreateSessionDialog({
    super.key,
    required this.maxParticipants,
    this.availableAreas,
  });

  @override
  State<CreateSessionDialog> createState() => _CreateSessionDialogState();
}

class _CreateSessionDialogState extends State<CreateSessionDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _areaController = TextEditingController();
  
  int _selectedMaxParticipants = 3;
  String? _selectedAreaId;
  bool _useCurrentLocation = true;

  @override
  void initState() {
    super.initState();
    _selectedMaxParticipants = widget.maxParticipants > 3 ? 5 : 3;
  }

  @override
  void dispose() {
    _areaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Group Walkabout'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAreaSelection(),
              const SizedBox(height: 20),
              _buildParticipantLimit(),
              const SizedBox(height: 16),
              _buildSubscriptionInfo(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createSession,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Create'),
        ),
      ],
    );
  }

  Widget _buildAreaSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Walkabout Area',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        
        // Current Location Option
        RadioListTile<bool>(
          title: const Text('Use Current Location'),
          subtitle: const Text('Create session at your current location'),
          value: true,
          groupValue: _useCurrentLocation,
          onChanged: (value) {
            setState(() {
              _useCurrentLocation = value!;
              if (_useCurrentLocation) {
                _selectedAreaId = null;
                _areaController.clear();
              }
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
        
        // Specific Area Option
        RadioListTile<bool>(
          title: const Text('Specific Area'),
          subtitle: const Text('Choose a predefined area'),
          value: false,
          groupValue: _useCurrentLocation,
          onChanged: (value) {
            setState(() {
              _useCurrentLocation = !value!;
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
        
        // Area Selection Field
        if (!_useCurrentLocation) ...[
          const SizedBox(height: 8),
          if (widget.availableAreas != null && widget.availableAreas!.isNotEmpty)
            DropdownButtonFormField<String>(
              value: _selectedAreaId,
              decoration: const InputDecoration(
                labelText: 'Select Area',
                border: OutlineInputBorder(),
              ),
              items: widget.availableAreas!.map((area) {
                return DropdownMenuItem(
                  value: area,
                  child: Text(area),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedAreaId = value;
                });
              },
              validator: (value) {
                if (!_useCurrentLocation && (value == null || value.isEmpty)) {
                  return 'Please select an area';
                }
                return null;
              },
            )
          else
            TextFormField(
              controller: _areaController,
              decoration: const InputDecoration(
                labelText: 'Area Name',
                hintText: 'Enter area name or description',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (!_useCurrentLocation && (value == null || value.isEmpty)) {
                  return 'Please enter an area name';
                }
                return null;
              },
            ),
        ],
      ],
    );
  }

  Widget _buildParticipantLimit() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Maximum Participants',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '$_selectedMaxParticipants participants',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    'Max: ${widget.maxParticipants}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Slider(
                value: _selectedMaxParticipants.toDouble(),
                min: 2,
                max: widget.maxParticipants.toDouble(),
                divisions: widget.maxParticipants - 2,
                activeColor: AppColors.primary,
                onChanged: (value) {
                  setState(() {
                    _selectedMaxParticipants = value.round();
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionInfo() {
    final isPremium = widget.maxParticipants > 3;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isPremium ? Colors.amber[50] : Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isPremium ? Colors.amber[200]! : Colors.blue[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isPremium ? Icons.star : Icons.info_outline,
            color: isPremium ? Colors.amber[600] : Colors.blue[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              isPremium 
                  ? 'Premium: Up to ${widget.maxParticipants} participants'
                  : 'Free: Up to 3 participants. Upgrade for more!',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isPremium ? Colors.amber[800] : Colors.blue[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _createSession() {
    if (_formKey.currentState!.validate()) {
      final areaId = _useCurrentLocation 
          ? 'current_location' 
          : (_selectedAreaId ?? _areaController.text.trim());
      
      Navigator.of(context).pop({
        'areaId': areaId,
        'maxParticipants': _selectedMaxParticipants,
        'useCurrentLocation': _useCurrentLocation,
      });
    }
  }
}