{"project_info": {"project_number": "873787664908", "project_id": "safestride-bd853", "storage_bucket": "safestride-bd853.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:873787664908:android:c4eef6f13930c481121c29", "android_client_info": {"package_name": "com.melur.safestride"}}, "oauth_client": [{"client_id": "873787664908-q3v8evm947nh4gcrkuoar48uuofn14jv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.melur.safestride", "certificate_hash": "486e70d685e1788103f91d9506d15f37573d3c8f"}}, {"client_id": "873787664908-hmnjl4gb0hrf8r2jfq92jk69dm6vs3rq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB6kagvRLUGoXD1FQKHD7U_Z5yerG9BOSs"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "873787664908-hmnjl4gb0hrf8r2jfq92jk69dm6vs3rq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "873787664908-8c<PERSON><PERSON><PERSON>buaoqk5llakuq11s0rrk0se.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.melur.safestride"}}]}}}], "configuration_version": "1"}