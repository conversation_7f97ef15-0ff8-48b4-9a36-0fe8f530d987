# SafeStride Product Requirements Document (PRD)

## Goals and Background Context

### Goals

- Digitize workplace safety walkabouts to eliminate 70% lost paper forms and 60% illegible handwriting issues.
- Enable seamless offline hazard identification and team coordination with cloud-based sync for 85% of users in poor connectivity areas.
- Provide compliance-ready reporting (e.g., ISO 45001, Gemba walks) to address 80% of tracking issues.
- Support 90% of users’ need for photo documentation with AI-driven hazard tagging.
- Offer a freemium model for small teams while providing scalable premium features for enterprises.

### Background Context

SafeStride addresses critical inefficiencies in workplace safety inspections, particularly in industrial settings like warehouses, factories, and offices, where paper-based processes lead to lost forms, illegible handwriting, and poor tracking. By offering a Flutter-based mobile app with offline-first functionality, AI hazard detection, and Firebase-backed collaboration, it meets the needs of moderately tech-savvy users (Leaders and Observers) while ensuring compliance with standards like ISO 45001. The competitive landscape (e.g., iAuditor) highlights demand for freemium, offline-capable solutions, but SafeStride differentiates with AI-driven tagging and enterprise scalability.

### Change Log

| Date       | Version | Description                                                                 | Author |
|------------|---------|-----------------------------------------------------------------------------|--------|
| 2025-07-10 | 1.0     | Initial PRD with core features, freemium model, and Firebase architecture.   | Grok   |
| 2025-07-10 | 2.0     | Removed speech_to_text, added limited AI tagging in Free tier, reduced roles to Leader/Observer, added role selection and QR code/invite link features. | Grok   |
| 2025-01-28 | 3.0     | Enhanced PRD with user flow diagrams, error handling strategies, priority labels, outcome-focused requirements, backup/recovery procedures, and expanded privacy controls. | BMad PM |

## Requirements

### Functional Requirements (Priority: Critical/High/Medium/Low)

- **FR1 [Critical]**: Users must select their role (Leader or Observer) to access appropriate workflows and features for their safety inspection responsibilities.
- **FR2 [Critical]**: Leaders must be able to conduct individual safety inspections by completing standardized checklists, documenting hazards with photos and descriptions, and sharing findings with stakeholders.
- **FR3 [High]**: Leaders must be able to coordinate team-based safety inspections by inviting team members and managing collaborative inspection sessions.
- **FR4 [High]**: Observers must be able to join collaborative inspection sessions and contribute their findings to the shared inspection process.
- **FR5 [High]**: Users must receive automated assistance in categorizing safety hazards from photos to improve documentation speed and consistency.
- **FR6 [High]**: Leaders must be able to review all inspection findings, resolve duplicate entries, and assign follow-up actions to ensure hazard remediation.
- **FR7 [Medium]**: Premium users must be able to manage safety inspections across multiple facilities to support enterprise-scale operations.
- **FR8 [Medium]**: Users must be able to generate compliance-ready reports in standard formats to meet regulatory and audit requirements.
- **FR9 [Low]**: Users must be able to use the app in their preferred language and practice with sample data to improve adoption and usability.
- **FR10 [Medium]**: Leaders must be able to organize inspection locations hierarchically to support structured safety management processes.

### Non-Functional

- NFR1: The app must operate offline with SQLite caching, syncing to Firebase when online, supporting 85% of users in poor connectivity areas.
- NFR2: UI actions must complete in <1 second to ensure responsiveness for glove-wearing users.
- NFR3: Photos must be compressed to 250KB to support 100 findings/photos offline within storage limits (100MB Free, 1TB Premium).
- NFR4: The app must support iOS 12.0+ (iPhone 7+) and Android 8.0+ for broad device compatibility.
- NFR5: Data must be secured with HTTPS sync, encrypted SQLite, and Firestore security rules (e.g., Observers access own findings, Leaders access session data).
- NFR6: The app must handle 100 sessions/month without performance degradation.
- NFR7: AI models (MobileNetV2 <4MB, MiniLM <5MB) must run offline on-device for hazard tagging and duplicate detection.

## User Interface Design Goals

### Overall UX Vision

Provide a simple, accessible interface for moderately tech-savvy users in industrial settings, prioritizing large touch targets, high-contrast displays, and clear feedback (e.g., sync status, task notifications) to ensure usability under gloves and in poor lighting.

### Key Interaction Paradigms

- **Role-Based Navigation**: Users select Leader or Observer role post-login, directing to role-specific flows (Solo/Group Walkabout for Leaders, session joining for Observers).
- **3-Tap Workflow**: Core actions (e.g., start walkabout, submit hazard) are accessible within 3 taps from the home screen.
- **Offline Feedback**: Clear indicators (e.g., "Offline Sync: Queued") for data status in poor connectivity areas.
- **QR Code Interaction**: Observers scan QR codes to join sessions; Leaders share QR codes or links via WhatsApp, SMS, or email.

### User Flow Diagrams

#### Leader Solo Walkabout Flow

| Step | Screen | Action | Next Screen |
|------|--------|--------|-------------|
| 1 | Login | Enter credentials → | Role Selection |
| 2 | Role Selection | Select "Leader" → | Home (Leader) |
| 3 | Home (Leader) | Tap "Solo Walkabout" → | Area Selection |
| 4 | Area Selection | Select/Create Area → | Walkabout Screen |
| 5 | Walkabout Screen | Complete checklist items → | Hazard Documentation |
| 6 | Hazard Documentation | Add hazards (photo, description, severity) → | Review Hub |
| 7 | Review Hub | Review findings, export CSV/PDF → | Home (Leader) |

#### Observer Group Walkabout Flow

| Step | Screen | Action | Next Screen |
|------|--------|--------|-------------|
| 1 | Login | Enter credentials → | Role Selection |
| 2 | Role Selection | Select "Observer" → | Session Join |
| 3 | Session Join | Scan QR code → | Join Request |
| 4 | Join Request | Wait for Leader approval → | Walkabout Screen |
| 5 | Walkabout Screen | Submit assigned checklist sections → | Hazard Documentation |
| 6 | Hazard Documentation | Add hazards (photo, description, severity) → | Follow-Up Tracker |
| 7 | Follow-Up Tracker | View assigned tasks → | Session Join |

#### Leader Group Walkabout Flow

| Step | Screen | Action | Next Screen |
|------|--------|--------|-------------|
| 1 | Home (Leader) | Tap "Group Walkabout" → | Session Setup |
| 2 | Session Setup | Select Area, generate QR/invite link → | Review Hub |
| 3 | Review Hub | Accept/reject Observer requests → | Walkabout Screen |
| 4 | Walkabout Screen | Complete own checklist sections → | Review Hub |
| 5 | Review Hub | Review all findings, merge duplicates → | Report Generation |
| 6 | Report Generation | Export PDF/CSV, assign follow-ups → | Home (Leader) |

### Error Handling & Recovery Strategies

#### Network Connectivity Issues

- **Offline Mode**: All core functionality (checklist, hazard documentation, photo capture) works offline with SQLite caching
- **Sync Queue**: Failed sync operations are queued and retried automatically when connectivity returns
- **Status Indicators**: Clear visual feedback shows sync status ("Synced", "Queued", "Syncing", "Failed")
- **Manual Retry**: Users can manually trigger sync attempts via pull-to-refresh gesture

#### Authentication Failures

- **Credential Caching**: Valid credentials cached locally for offline login
- **Session Recovery**: Automatic re-authentication when session expires
- **Fallback Login**: Alternative authentication methods (email/password if SSO fails)
- **Error Messages**: Clear, actionable error messages with retry options

#### Data Corruption & Loss Prevention

- **Local Backup**: Critical data (findings, photos) automatically backed up to device storage
- **Incremental Sync**: Only changed data synced to minimize corruption risk
- **Conflict Resolution**: Timestamp-based conflict resolution for concurrent edits
- **Recovery Mode**: Ability to restore from local backup if cloud sync fails

#### AI Model Failures

- **Graceful Degradation**: Manual tagging available when AI models fail
- **Model Fallback**: Simplified tagging categories if advanced models unavailable
- **Error Logging**: AI failures logged for debugging without blocking user workflow
- **Offline Capability**: AI models run locally to avoid network dependency

#### User Input Errors

- **Validation**: Real-time input validation with clear error messages
- **Auto-save**: Progress automatically saved to prevent data loss
- **Confirmation Dialogs**: Critical actions (delete, export) require confirmation
- **Undo Functionality**: Recent actions can be undone within session

### Core Screens and Views

- **Login Screen**: Email/password or SSO (Google) with offline credential caching.
- **Role Selection Screen**: Choose Leader or Observer role post-login.
- **Home Screen (Leader)**: Options for Solo Walkabout, Group Walkabout, Sites tab (Premium), and Subscription tab.
- **Walkabout Screen**: Area selection, 10-item checklist, hazard form (description, severity, photo, AI tags).
- **Review Hub (Leader)**: View findings, accept/reject Observers, merge duplicates, assign follow-ups, generate reports.
- **Session Join Screen (Observer)**: QR code scanner to request joining a Group Walkabout.
- **Follow-Up Tracker**: View and update assigned tasks (both roles).
- **Sites Tab (Premium)**: Switch between Sites, manage Areas/Sites.
- **Practice Mode**: Sample data for onboarding and training.

### Accessibility

WCAG-compliant with large touch targets, high-contrast UI, adjustable fonts, and color-blind-friendly indicators to support diverse users in industrial environments.

### Branding

No specific branding elements provided; adopt a clean, professional design with neutral colors to align with workplace safety focus. Use consistent icons and terminology (e.g., ISO 45001 standards) for compliance credibility.

### Target Device and Platforms

Native mobile app for iOS 12.0+ (iPhone 7+) and Android 8.0+, built with Flutter for cross-platform consistency.

**Assumptions Made**:

- No specific branding guidelines were provided, so a neutral, professional style was assumed.
- Practice Mode was inferred as a core screen for onboarding, based on the PRD’s emphasis on moderate tech comfort.

**Questions for Clarification**:

- Are there specific branding elements (e.g., color palette, logo) to incorporate?
- Should the Role Selection Screen include additional onboarding prompts for first-time users?
- Are there specific WCAG levels (e.g., AA, AAA) to target for accessibility?

## Technical Assumptions

### Repository Structure

Monorepo, as Flutter’s single codebase supports both iOS and Android, with Firebase services managed in a unified project structure.

### Service Architecture

Serverless architecture using Google Firebase (Authentication, Firestore, Analytics, Hosting) for scalable backend services, with SQLite for local offline caching. This aligns with the need for offline-first functionality and enterprise scalability.

### Testing Requirements

- Unit tests for core logic (e.g., AI tagging, data sync).
- Integration tests for Firebase Authentication, Firestore sync, and SQLite caching.
- End-to-end (E2E) tests for key user flows (login, role selection, walkabout creation, report generation).
- Manual testing convenience methods for QR code scanning and AI model accuracy validation in industrial settings.

### Additional Technical Assumptions and Requests

- **Flutter**: Chosen for cross-platform development to reduce effort for a single developer, supporting iOS and Android with one codebase.
- **Google Firebase**: Selected for authentication (email/password, SSO), Firestore for data storage/sync, Analytics for usage tracking, and Hosting for scalability, aligning with offline-first and enterprise needs.
- **Plugins**: `image_picker` for photo capture (250KB compression), `tflite_flutter` for AI (MobileNetV2, MiniLM), `mobile_scanner` and `qr_flutter` for QR code functionality, `share_plus` for invitation links, `pdf` for report generation, `googleapis` for cloud storage, `in_app_purchase` and `stripe_payment` for monetization, `flutter_local_notifications` for task alerts.
- **Security**: HTTPS for cloud sync, encrypted SQLite, and Firestore rules ensure data privacy (e.g., role-based access).
- **Performance**: Optimized for <1-second UI actions and 100 sessions/month, with photo compression to manage storage (100MB Free, 1TB Premium).

### Backup, Recovery & Maintenance

#### Data Backup Strategy

- **Local Backup**: Critical inspection data (findings, photos, checklists) automatically backed up to device storage every 24 hours
- **Cloud Backup**: All user data synced to Firebase Firestore with automatic daily backups to Google Cloud Storage
- **Incremental Backup**: Only changed data backed up to minimize storage usage and sync time
- **Backup Retention**: Local backups retained for 30 days, cloud backups retained for 1 year (Free) / 3 years (Premium)

#### Recovery Procedures

- **Device Loss/Replacement**: Users can restore all data by logging in on new device (cloud sync)
- **Data Corruption**: Local backup automatically restored if corruption detected during app startup
- **Sync Conflicts**: Timestamp-based conflict resolution with user notification for manual review if needed
- **Account Recovery**: Password reset and account recovery through Firebase Authentication

#### Maintenance & Support

- **App Updates**: Over-the-air updates via app stores with backward compatibility for data formats
- **Database Maintenance**: Automated cleanup of expired data (completed follow-ups >1 year old)
- **Performance Monitoring**: Firebase Analytics tracks app performance and crash reports
- **User Support**: In-app help documentation and contact form for technical support

### Privacy & Data Protection

#### Data Collection & Usage

- **Personal Data**: Email, name, role, and device ID collected for authentication and app functionality
- **Inspection Data**: Photos, descriptions, locations, and timestamps stored for safety documentation purposes
- **Usage Analytics**: Anonymized usage patterns collected to improve app performance and features
- **No Third-Party Sharing**: User data never shared with third parties except as required by law

#### Data Retention & Deletion

- **Active Data**: Inspection findings retained indefinitely while user account is active
- **Inactive Accounts**: Data automatically deleted after 2 years of account inactivity
- **User-Requested Deletion**: Complete data deletion within 30 days of user request
- **Legal Compliance**: Data retained as required by workplace safety regulations (typically 3-7 years)

#### User Consent & Control

- **Initial Consent**: Users must accept privacy policy and terms of service during account creation
- **Data Access**: Users can download all their data in standard formats (CSV, PDF) at any time
- **Granular Controls**: Users can disable analytics collection and photo location tagging in settings
- **Consent Withdrawal**: Users can request account deletion and data removal at any time

#### Security Measures

- **Encryption**: All data encrypted in transit (HTTPS) and at rest (encrypted SQLite, Firestore)
- **Access Control**: Role-based access ensures users only see data they're authorized to view
- **Authentication**: Multi-factor authentication available for enhanced security
- **Audit Trail**: All data access and modifications logged for security monitoring

## Epics (Priority: Critical/High/Medium/Low)

- **Epic1 [Critical] Foundation & Core Infrastructure**: Establish app setup, Firebase integration, role selection, and basic walkabout functionality.
- **Epic2 [High] Collaborative Walkabout & AI Tagging**: Enable Group Walkabout with QR code/invite link features and AI hazard tagging.
- **Epic3 [Medium] Advanced Features & Reporting**: Deliver Premium features like multi-site support, advanced reporting, and AI duplicate detection.
- **Epic4 [Low] Usability & Monetization**: Implement multi-language support, Practice Mode, and freemium monetization.

**Rationale**: Four epics balance delivering incremental value while keeping stories manageable for a single developer. Epic1 establishes the foundation (app, login, basic walkabout), Epic2 adds collaboration and AI, Epic3 scales for enterprises, and Epic4 polishes usability and revenue. Fewer epics were considered, but splitting ensures clear milestones. Cross-cutting concerns (e.g., security, offline caching) are integrated across stories.

**Questions for Clarification**:

- Should epics be consolidated (e.g., merge Epic3 and Epic4) for faster MVP delivery?
- Are there specific priorities for Premium features to split Epic3 further?

## Epic 1 Foundation & Core Infrastructure

Establish the core app with Firebase setup, role selection, login, and Solo Walkabout functionality to provide immediate value for individual safety inspections while setting up infrastructure for future features.

### Story 1.1 [Critical] Project Setup & Firebase Integration

As a developer, I want to establish the foundational app infrastructure, so that users can securely access the app and their data is reliably stored both online and offline.

#### Acceptance Criteria

- 1.1.1: Flutter project created with iOS 12.0+ and Android 8.0+ support.
- 1.1.2: Firebase Authentication (email/password, Google SSO) integrated with offline credential caching in SQLite.
- 1.1.3: Firestore configured for storing users, sessions, findings, and areas.
- 1.1.4: SQLite (`sqflite`) implemented for offline caching of login credentials and basic data models.
- 1.1.5: Basic health-check route displays a “Welcome to SafeStride” screen.
- 1.1.6: Unit tests for Firebase and SQLite integration.

### Story 1.2 [Critical] Role Selection & Login UI

As a user, I want to securely access the app and specify my role, so that I can use features appropriate to my safety inspection responsibilities.

#### Acceptance Criteria

- 1.2.1: Login screen supports email/password and SSO (Google) via Firebase Authentication.
- 1.2.2: Role Selection screen displays post-login with “Leader” and “Observer” options (large touch targets, high-contrast).
- 1.2.3: Offline credential caching allows login in poor connectivity areas.
- 1.2.4: First-time users see a 30-day Premium trial prompt and onboarding tutorial.
- 1.2.5: E2E tests for login and role selection flows.
- 1.2.6: WCAG-compliant UI (large fonts, color-blind-friendly).

### Story 1.3 [Critical] Solo Walkabout & Area Management

As a Leader, I want to conduct individual safety inspections and organize locations, so that I can systematically document hazards and share findings with stakeholders.

#### Acceptance Criteria

- 1.3.1: Home screen offers “Solo Walkabout” option post-Leader role selection.
- 1.3.2: Area selection dropdown lists Areas (default Site in Free tier) from SQLite/Firestore.
- 1.3.3: Leaders can add/edit Area names (e.g., “Warehouse A” to “Storage Zone A”).
- 1.3.4: Checklist form includes 10 items (Pass/Fail, notes, 250KB photos via `image_picker`).
- 1.3.5: Hazard form includes description, severity (Low/Medium/High), and photo.
- 1.3.6: Findings saved in SQLite, synced to Firestore when online, with sync status UI.
- 1.3.7: CSV export shareable via email.
- 1.3.8: Unit and E2E tests for walkabout creation and export.

## Epic 2 Collaborative Walkabout & AI Tagging

Enable Group Walkabout with QR code and invite link features, integrate AI hazard tagging, and support Observer collaboration to streamline team-based inspections.

### Story 2.1 [High] Group Walkabout Setup & Invitations

As a Leader, I want to coordinate team-based safety inspections, so that multiple team members can collaborate efficiently on comprehensive safety reviews.

#### Acceptance Criteria

- 2.1.1: Home screen offers “Group Walkabout” option for Leaders.
- 2.1.2: Session creation includes Area selection and team setup (max 2 Observers in Free tier, unlimited in Premium).
- 2.1.3: Leaders generate invitation links (WhatsApp, SMS, email via `share_plus`) or QR codes (`qr_flutter`) for Observers.
- 2.1.4: Review Hub displays Observer join requests for Leader to accept/reject.
- 2.1.5: Session data stored in SQLite/Firestore with `invite_link` and `qr_code` fields.
- 2.1.6: E2E tests for session creation and invitation flows.

### Story 2.2 [High] AI Model Infrastructure Setup

As a developer, I want to establish AI model infrastructure and dependencies, so that hazard auto-tagging features can be reliably implemented and maintained.

#### Acceptance Criteria

- 2.2.1: TensorFlow Lite Flutter plugin (`tflite_flutter` v0.10.0) integrated with proper Android/iOS native dependencies.
- 2.2.2: AI model storage directory structure created in app documents (`/models/mobilenet_v2/`, `/models/minilm/`).
- 2.2.3: Model metadata management system implemented with version checking and automatic updates via Firebase Storage.
- 2.2.4: MobileNetV2 model (<4MB) and MiniLM model (<5MB) downloaded and validated during app initialization.
- 2.2.5: AI model loading and inference pipeline established with error handling and graceful degradation.
- 2.2.6: Model performance benchmarking on target devices (inference time <500ms, memory usage <50MB).
- 2.2.7: Unit tests for model loading, inference pipeline, and fallback mechanisms.
- 2.2.8: Integration tests for model download, validation, and update workflows.

### Story 2.3 [High] Observer Joining & Submission

As an Observer, I want to participate in team safety inspections, so that I can contribute my expertise to comprehensive safety assessments.

#### Acceptance Criteria

- 2.3.1: Session Join screen allows QR code scanning (`mobile_scanner`) to request joining a session.
- 2.3.2: Observers submit checklist sections (Pass/Fail, notes, photos) and hazards (description, severity, photo).
- 2.3.3: Findings cached in SQLite, synced to Firestore, with sync status UI.
- 2.3.4: Observers receive notifications (`flutter_local_notifications`) for acceptance/rejection.
- 2.3.5: E2E tests for joining and submission flows.

### Story 2.4 [High] AI Hazard Auto-Tagging

As a user, I want automated assistance in categorizing safety hazards, so that I can document findings more quickly and consistently.

#### Acceptance Criteria

- 2.4.1: MobileNetV2 model suggests hazard tags offline using established AI infrastructure (Free: 3 tags/session, 5 categories; Premium: unlimited, 9-10 categories).
- 2.4.2: Categories include Spill, Blocked Exit, Electrical Issue, Trip Hazard, Fire Hazard (Free); adds Chemical Spill, Clutter, Poor Lighting, Ergonomic Issue (Premium).
- 2.4.3: Image preprocessing pipeline optimizes photos for AI inference (resize, normalize, format conversion).
- 2.4.4: Confidence scoring system filters low-quality suggestions (minimum 70% confidence threshold).
- 2.4.5: Users confirm/edit AI-suggested tags before submission, stored in SQLite/Firestore with confidence scores.
- 2.4.6: Fallback to manual tagging when AI models unavailable or inference fails.
- 2.4.7: Unit tests for AI model accuracy and tag relevance; manual tests with industrial safety image dataset.

## Epic 3 Advanced Features & Reporting

Deliver Premium features like multi-site support, advanced reporting, and AI duplicate detection to scale for enterprise needs and compliance.

### Story 3.1 [Medium] Multi-Site Support

As a Leader (Premium), I want to manage safety inspections across multiple facilities, so that I can maintain consistent safety standards enterprise-wide.

#### Acceptance Criteria

- 3.1.1: Sites tab allows adding/editing Site names and switching between Sites.
- 3.1.2: Each Site links to Areas, stored in Firestore (`{id, name, areas: [area_id]}`).
- 3.1.3: Free tier limited to 1 Site; Premium supports multiple Sites.
- 3.1.4: E2E tests for Site switching and Area management.

### Story 3.2 [Medium] Advanced Reporting

As a Leader (Premium), I want comprehensive, customizable safety reports, so that I can meet regulatory compliance requirements and demonstrate safety program effectiveness.

#### Acceptance Criteria

- 3.2.1: Review Hub supports PDF/CSV export with ISO 45001/Gemba templates (Premium).
- 3.2.2: Reports include audit trails, shareable via email or Google Drive/Dropbox (`googleapis`).
- 3.2.3: Free tier limited to basic CSV export.
- 3.2.4: E2E tests for report generation and sharing.

### Story 3.3 [Medium] AI Duplicate Detection

As a Leader (Premium), I want automated identification of duplicate safety findings, so that I can efficiently review inspection results and avoid redundant follow-up actions.

#### Acceptance Criteria

- 3.3.1: MiniLM model (established in Story 2.2) analyzes text similarity between finding descriptions using semantic embeddings.
- 3.3.2: Image hash comparison algorithm detects visually similar photos with configurable similarity thresholds (85% default).
- 3.3.3: Duplicate detection pipeline combines text and image analysis with weighted scoring system.
- 3.3.4: Merge suggestions displayed in Review Hub with confidence scores and side-by-side comparison UI.
- 3.3.5: Leaders can accept/reject merge suggestions, with decisions stored in SQLite/Firestore for learning.
- 3.3.6: Batch processing capability for analyzing existing findings when feature is first enabled.
- 3.3.7: Unit tests for duplicate detection accuracy using curated test dataset (>90% precision target).
- 3.3.8: Performance optimization ensures duplicate analysis completes within 2 seconds for 100 findings.

## Epic 4 Usability & Monetization

Implement multi-language support, Practice Mode, and freemium monetization to enhance accessibility and drive revenue.

### Story 4.1 [Low] Multi-Language & Practice Mode

As a user, I want to use the app in my preferred language and practice with sample data, so that I can effectively learn and adopt the safety inspection system.

#### Acceptance Criteria

- 4.1.1: UI supports English, Spanish, Mandarin, Hindi, switchable in settings.
- 4.1.2: Practice Mode provides sample data for walkabout and review flows.
- 4.1.3: WCAG-compliant UI with large touch targets, high-contrast, color-blind-friendly.
- 4.1.4: E2E tests for language switching and Practice Mode.

### Story 4.2 [Low] Freemium Monetization

As a user, I want transparent pricing options with clear value propositions, so that I can choose the service level that best meets my safety inspection needs.

#### Acceptance Criteria

- 4.2.1: Free tier limits: 2 Observers, 100MB storage, 3 AI tags/session, 1 Site.
- 4.2.2: Premium tier ($5/month or $50/year) unlocks unlimited Observers, 1TB storage, unlimited AI tags, multi-site support, PDF reports.
- 4.2.3: Subscription tab displays plans, trial prompt, and upgrade via `in_app_purchase`/`stripe_payment`.
- 4.2.4: Upselling prompts when limits exceeded (e.g., >2 Observers).
- 4.2.5: E2E tests for subscription flows.

## Checklist Results Report

I have drafted the complete updated PRD based on the provided template and all prior discussions. Before running the `pm-checklist` and populating the results here, please confirm if you want to proceed with the checklist execution or make further revisions to the PRD.

## Next Steps

### Design Architect Prompt

Using the SafeStride PRD, create a high-level UI/UX design focusing on role-based navigation, offline-first usability, and WCAG-compliant accessibility for industrial users, ensuring 3-tap navigation and clear feedback for sync status and task notifications.

### Architect Prompt

Using the SafeStride PRD, design a serverless architecture with Flutter for cross-platform mobile, Google Firebase for authentication/data/sync, and SQLite for offline caching, optimizing for <1-second UI actions and 100 sessions/month scalability.
