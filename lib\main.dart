import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/firebase_service.dart';
import 'services/database_service.dart';
import 'services/auth_service.dart';
import 'providers/auth_provider.dart';
import 'providers/group_session_provider.dart';
import 'repositories/repository_factory.dart';
import 'screens/welcome_screen.dart';
import 'screens/group_walkabout_screen.dart';
import 'utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await FirebaseService.initialize();

    // Initialize local database
    await DatabaseService.database;

    runApp(const SafeStrideApp());
  } catch (e) {
    // If Firebase initialization fails, run app in offline mode
    runApp(const SafeStrideApp());
  }
}

class SafeStrideApp extends StatelessWidget {
  const SafeStrideApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AuthProvider(
            AuthService(RepositoryFactory().createHybridUserRepository()),
            RepositoryFactory().createHybridUserRepository(),
          ),
        ),
        ChangeNotifierProvider(
          create: (_) => GroupSessionProvider(),
        ),
      ],
      child: MaterialApp(
        title: 'SafeStride',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const WelcomeScreen(),
        debugShowCheckedModeBanner: false,
        routes: {
          '/group-walkabout': (context) => const GroupWalkaboutScreen(),
        },
      ),
    );
  }
}
